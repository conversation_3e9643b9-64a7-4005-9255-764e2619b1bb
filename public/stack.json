{"saasTypes": {"ecommerce": {"name": "E-commerce Platform", "description": "Online store with product catalog, shopping cart, and payment processing", "techStack": {"frontend": {"primary": ["Next.js", "React", "TypeScript"], "styling": ["Tailwind CSS", "Styled Components"], "stateManagement": ["Zustand", "Redux Toolkit"], "alternatives": ["Vue.js", "Angular", "Svelte"]}, "backend": {"primary": ["Node.js", "Express.js"], "alternatives": ["Python/Django", "Ruby on Rails", "PHP/Laravel", "Java/Spring"]}, "database": {"primary": ["PostgreSQL"], "alternatives": ["MySQL", "MongoDB", "Supabase"]}, "auth": {"primary": ["NextAuth.js", "Auth0"], "alternatives": ["Firebase Auth", "Supabase Auth", "Clerk"]}, "payments": {"primary": ["Stripe", "PayPal"], "alternatives": ["Square", "Braintree"]}, "deployment": {"primary": ["Vercel", "Netlify"], "alternatives": ["AWS", "Google Cloud", "DigitalOcean"]}, "storage": {"primary": ["AWS S3", "Cloudinary"], "alternatives": ["Google Cloud Storage", "Azure Blob"]}}}, "crm": {"name": "Customer Relationship Management", "description": "Manage customer interactions, sales pipeline, and business relationships", "techStack": {"frontend": {"primary": ["React", "TypeScript", "Next.js"], "styling": ["Tailwind CSS", "Material-UI"], "stateManagement": ["React Query", "Zustand"], "alternatives": ["Vue.js", "Angular"]}, "backend": {"primary": ["Node.js", "Express.js", "GraphQL"], "alternatives": ["Python/FastAPI", "C#/.NET", "Java/Spring"]}, "database": {"primary": ["PostgreSQL", "Redis"], "alternatives": ["MySQL", "MongoDB"]}, "auth": {"primary": ["Auth0", "Firebase Auth"], "alternatives": ["Okta", "AWS Cognito"]}, "realtime": {"primary": ["Socket.io", "WebSockets"], "alternatives": ["<PERSON><PERSON><PERSON>", "Ably"]}, "deployment": {"primary": ["AWS", "<PERSON>er"], "alternatives": ["Google Cloud", "Azure"]}, "analytics": {"primary": ["Mixpanel", "Amplitude"], "alternatives": ["Google Analytics", "PostHog"]}}}, "analytics": {"name": "Analytics Dashboard", "description": "Data visualization and business intelligence platform", "techStack": {"frontend": {"primary": ["React", "D3.js", "Chart.js"], "styling": ["Tailwind CSS", "Ant Design"], "stateManagement": ["Redux Toolkit", "React Query"], "alternatives": ["Vue.js", "Angular"]}, "backend": {"primary": ["Python", "FastAPI", "<PERSON><PERSON>"], "alternatives": ["Node.js", "R/Shiny", "Scala/Spark"]}, "database": {"primary": ["PostgreSQL", "ClickHouse", "Redis"], "alternatives": ["<PERSON><PERSON><PERSON><PERSON>", "Snowflake", "MongoDB"]}, "dataProcessing": {"primary": ["Apache Kafka", "Apache Airflow"], "alternatives": ["RabbitMQ", "Celery"]}, "auth": {"primary": ["Auth0", "JWT"], "alternatives": ["Firebase Auth", "Okta"]}, "deployment": {"primary": ["AWS", "<PERSON>er", "Kubernetes"], "alternatives": ["Google Cloud", "Azure"]}, "monitoring": {"primary": ["<PERSON><PERSON>", "Prometheus"], "alternatives": ["DataDog", "New Relic"]}}}, "saas_starter": {"name": "SaaS Starter Kit", "description": "Basic SaaS application with user management and subscription billing", "techStack": {"frontend": {"primary": ["Next.js", "React", "TypeScript"], "styling": ["Tailwind CSS", "Shadcn/ui"], "stateManagement": ["Zustand", "React Query"], "alternatives": ["Vue.js", "Svelte"]}, "backend": {"primary": ["Next.js API Routes", "Prisma"], "alternatives": ["Node.js/Express", "Supabase", "Firebase"]}, "database": {"primary": ["PostgreSQL", "Supabase"], "alternatives": ["MySQL", "PlanetScale"]}, "auth": {"primary": ["NextAuth.js", "Clerk"], "alternatives": ["Supabase Auth", "Auth0"]}, "payments": {"primary": ["Stripe", "Lemonsqueezy"], "alternatives": ["Paddle", "PayPal"]}, "deployment": {"primary": ["Vercel", "Railway"], "alternatives": ["Netlify", "AWS"]}, "email": {"primary": ["Resend", "SendGrid"], "alternatives": ["Mailgun", "AWS SES"]}}}, "marketplace": {"name": "Marketplace Platform", "description": "Multi-vendor platform connecting buyers and sellers", "techStack": {"frontend": {"primary": ["Next.js", "React", "TypeScript"], "styling": ["Tailwind CSS", "Framer Motion"], "stateManagement": ["Zustand", "React Query"], "alternatives": ["Vue.js", "Angular"]}, "backend": {"primary": ["Node.js", "Express.js", "GraphQL"], "alternatives": ["Python/Django", "Ruby on Rails"]}, "database": {"primary": ["PostgreSQL", "Redis", "Elasticsearch"], "alternatives": ["MongoDB", "MySQL"]}, "auth": {"primary": ["Auth0", "Multi-tenant setup"], "alternatives": ["Firebase Auth", "Okta"]}, "payments": {"primary": ["Stripe Connect", "Split payments"], "alternatives": ["PayPal Marketplace", "<PERSON><PERSON><PERSON>"]}, "search": {"primary": ["Elasticsearch", "Algolia"], "alternatives": ["Solr", "Typesense"]}, "deployment": {"primary": ["AWS", "<PERSON>er", "Kubernetes"], "alternatives": ["Google Cloud", "Azure"]}, "messaging": {"primary": ["Socket.io", "Redis Pub/Sub"], "alternatives": ["<PERSON><PERSON><PERSON>", "AWS SNS"]}}}}, "techCategories": {"frontend": {"name": "Frontend", "color": "#3B82F6", "description": "User interface and client-side logic"}, "backend": {"name": "Backend", "color": "#10B981", "description": "Server-side logic and APIs"}, "database": {"name": "Database", "color": "#F59E0B", "description": "Data storage and management"}, "auth": {"name": "Authentication", "color": "#EF4444", "description": "User authentication and authorization"}, "payments": {"name": "Payments", "color": "#8B5CF6", "description": "Payment processing and billing"}, "deployment": {"name": "Deployment", "color": "#06B6D4", "description": "Hosting and infrastructure"}, "storage": {"name": "Storage", "color": "#84CC16", "description": "File and media storage"}, "analytics": {"name": "Analytics", "color": "#F97316", "description": "Data tracking and insights"}, "realtime": {"name": "Real-time", "color": "#EC4899", "description": "Live updates and messaging"}, "search": {"name": "Search", "color": "#6366F1", "description": "Search and discovery features"}, "email": {"name": "Email", "color": "#14B8A6", "description": "Email delivery and notifications"}, "monitoring": {"name": "Monitoring", "color": "#64748B", "description": "Application monitoring and logging"}, "dataProcessing": {"name": "Data Processing", "color": "#DC2626", "description": "Data pipelines and ETL"}, "messaging": {"name": "Messaging", "color": "#7C3AED", "description": "Communication and notifications"}}}