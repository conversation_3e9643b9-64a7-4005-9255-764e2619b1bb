import { pgTable, serial, text, json, timestamp, boolean, integer } from 'drizzle-orm/pg-core';

// Technologies table - stores all framework/technology information
export const technologies = pgTable('technologies', {
  id: serial('id').primaryKey(),
  name: text('name').notNull().unique(),
  description: text('description').notNull(),
  pros: json('pros').$type<string[]>().notNull().default([]),
  cons: json('cons').$type<string[]>().notNull().default([]),
  imageUrl: text('image_url'), // For framework icons you're adding
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Tech categories table - Frontend, Backend, Database, etc.
export const techCategories = pgTable('tech_categories', {
  id: serial('id').primary<PERSON>ey(),
  key: text('key').notNull().unique(), // e.g., 'frontend', 'backend'
  name: text('name').notNull(), // e.g., 'Frontend', 'Backend'
  description: text('description').notNull(),
  color: text('color').notNull(), // Hex color for UI
  sortOrder: integer('sort_order').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// SaaS types table - predefined templates like e-commerce, CRM, etc.
export const saasTypes = pgTable('saas_types', {
  id: serial('id').primaryKey(),
  key: text('key').notNull().unique(), // e.g., 'ecommerce', 'crm'
  name: text('name').notNull(), // e.g., 'E-commerce Platform'
  description: text('description').notNull(),
  techStack: json('tech_stack').$type<{
    [categoryKey: string]: {
      primary: string[];
      alternatives?: string[];
      [key: string]: any;
    };
  }>().notNull().default({}),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Custom projects table - user-created projects
export const customProjects = pgTable('custom_projects', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description').notNull(),
  techStack: json('tech_stack').$type<{
    [categoryKey: string]: {
      primary: string[];
      alternatives: string[];
    };
  }>().notNull().default({}),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// User preferences table - for UI state and settings
export const userPreferences = pgTable('user_preferences', {
  id: serial('id').primaryKey(),
  key: text('key').notNull().unique(), // e.g., 'sidebar_state', 'theme', 'search_filters'
  value: json('value').$type<any>().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Technology usage tracking (optional - for analytics)
export const technologyUsage = pgTable('technology_usage', {
  id: serial('id').primaryKey(),
  technologyName: text('technology_name').notNull(),
  saasTypeKey: text('saas_type_key'), // null for custom projects
  projectId: integer('project_id'), // null for predefined saas types
  categoryKey: text('category_key').notNull(),
  isPrimary: boolean('is_primary').notNull().default(false),
  usageCount: integer('usage_count').default(1),
  lastUsed: timestamp('last_used').defaultNow().notNull(),
});

// Export types for TypeScript
export type Technology = typeof technologies.$inferSelect;
export type NewTechnology = typeof technologies.$inferInsert;

export type TechCategory = typeof techCategories.$inferSelect;
export type NewTechCategory = typeof techCategories.$inferInsert;

export type SaasType = typeof saasTypes.$inferSelect;
export type NewSaasType = typeof saasTypes.$inferInsert;

export type CustomProject = typeof customProjects.$inferSelect;
export type NewCustomProject = typeof customProjects.$inferInsert;

export type UserPreference = typeof userPreferences.$inferSelect;
export type NewUserPreference = typeof userPreferences.$inferInsert;

export type TechnologyUsage = typeof technologyUsage.$inferSelect;
export type NewTechnologyUsage = typeof technologyUsage.$inferInsert;
