-- Seed script for SaaS Tech Stack Planner
-- Run this directly in your Neon database console

-- 1. Insert Tech Categories
INSERT INTO tech_categories (key, name, description, color, sort_order) VALUES
('frontend', 'Frontend', 'User interface and client-side technologies', '#3B82F6', 1),
('backend', 'Backend', 'Server-side technologies and APIs', '#10B981', 2),
('database', 'Database', 'Data storage and management systems', '#F59E0B', 3),
('auth', 'Authentication', 'User authentication and authorization', '#EF4444', 4),
('payments', 'Payments', 'Payment processing and billing systems', '#8B5CF6', 5),
('hosting', 'Hosting & Deployment', 'Cloud hosting and deployment platforms', '#06B6D4', 6),
('monitoring', 'Monitoring', 'Application monitoring and analytics', '#84CC16', 7),
('email', 'Email', 'Email services and communication', '#F97316', 8),
('storage', 'File Storage', 'File and media storage solutions', '#EC4899', 9),
('realtime', 'Real-time', 'Real-time communication and updates', '#6366F1', 10),
('search', 'Search', 'Search and indexing solutions', '#14B8A6', 11),
('cms', 'Content Management', 'Content management systems', '#A855F7', 12)
ON CONFLICT (key) DO NOTHING;

-- 2. Insert Technologies (Part 1 - Frontend & Backend)
INSERT INTO technologies (name, description, pros, cons) VALUES
('Next.js', 'A React framework that enables functionality such as server-side rendering and generating static websites for React based web applications.',
 '["Server-side rendering", "Static site generation", "Built-in routing", "API routes", "Excellent performance"]',
 '["Learning curve", "Can be overkill for simple projects", "Vendor lock-in with Vercel"]'),

('React', 'A JavaScript library for building user interfaces, particularly web applications with dynamic and interactive elements.',
 '["Component-based architecture", "Large ecosystem", "Virtual DOM", "Strong community", "Reusable components"]',
 '["Steep learning curve", "Rapid ecosystem changes", "JSX syntax learning", "SEO challenges without SSR"]'),

('Vue.js', 'A progressive JavaScript framework for building user interfaces and single-page applications.',
 '["Easy to learn", "Excellent documentation", "Small bundle size", "Progressive adoption", "Great tooling"]',
 '["Smaller ecosystem than React", "Less job market demand", "Fewer resources", "Limited scalability for large apps"]'),

('Angular', 'A platform and framework for building single-page client applications using HTML and TypeScript.',
 '["Full framework solution", "TypeScript by default", "Powerful CLI", "Enterprise-ready", "Strong architecture"]',
 '["Steep learning curve", "Complex for small projects", "Frequent major updates", "Large bundle size"]'),

('Svelte', 'A radical new approach to building user interfaces with a compile-time optimized framework.',
 '["No virtual DOM", "Smaller bundle sizes", "Easy to learn", "Great performance", "Less boilerplate"]',
 '["Smaller ecosystem", "Fewer job opportunities", "Limited tooling", "Newer framework"]'),

('TypeScript', 'A strongly typed programming language that builds on JavaScript by adding static type definitions.',
 '["Type safety", "Better IDE support", "Catches errors early", "Great for large projects", "Excellent tooling"]',
 '["Learning curve", "Compilation step", "Can be verbose", "Setup complexity"]'),

('Tailwind CSS', 'A utility-first CSS framework packed with classes that can be composed to build any design.',
 '["Utility-first approach", "Highly customizable", "Small production builds", "Great developer experience", "Consistent design"]',
 '["Learning curve", "HTML can look cluttered", "Requires build process", "Can be verbose"]'),

('Styled Components', 'A library for React and React Native that allows you to use component-level styles written with a mixture of JavaScript and CSS.',
 '["CSS-in-JS", "Dynamic styling", "Automatic vendor prefixing", "No class name bugs", "Theme support"]',
 '["Runtime overhead", "Learning curve", "Bundle size increase", "Debugging complexity"]'),

('Material-UI', 'React components implementing Google Material Design principles and guidelines.',
 '["Pre-built components", "Material Design", "Accessibility built-in", "Theming system", "Large community"]',
 '["Large bundle size", "Design constraints", "Customization complexity", "Performance overhead"]'),

('Chakra UI', 'A simple, modular and accessible component library for React applications.',
 '["Simple API", "Accessibility focused", "Theming system", "TypeScript support", "Good documentation"]',
 '["Smaller ecosystem", "Limited design flexibility", "React only", "Newer library"]'),

('Node.js', 'A JavaScript runtime built on Chrome V8 JavaScript engine for building scalable network applications.',
 '["JavaScript everywhere", "Large ecosystem", "Fast development", "Great for APIs", "Active community"]',
 '["Single-threaded limitations", "Callback complexity", "Rapid ecosystem changes", "Memory consumption"]'),

('Express.js', 'A minimal and flexible Node.js web application framework providing robust features for web and mobile applications.',
 '["Minimalist framework", "Large ecosystem", "Easy to learn", "Flexible", "Great middleware support"]',
 '["Minimal structure", "Security concerns", "Callback hell", "Manual setup required"]'),

('Fastify', 'A fast and low overhead web framework for Node.js with a focus on performance and developer experience.',
 '["High performance", "TypeScript support", "Plugin architecture", "JSON schema validation", "Low overhead"]',
 '["Smaller ecosystem", "Less documentation", "Newer framework", "Learning curve"]'),

('Nest.js', 'A progressive Node.js framework for building efficient, reliable and scalable server-side applications.',
 '["TypeScript by default", "Modular architecture", "Dependency injection", "Enterprise-ready", "Great documentation"]',
 '["Learning curve", "Can be overkill", "Complex for simple projects", "Opinionated structure"]')
ON CONFLICT (name) DO NOTHING;

-- 3. Insert Technologies (Part 2 - Database & Backend continued)
INSERT INTO technologies (name, description, pros, cons) VALUES
('PostgreSQL', 'A powerful, open source object-relational database system with strong reputation for reliability and performance.',
 '["ACID compliance", "Extensible", "Standards compliant", "Strong community", "Advanced features"]',
 '["Complex for beginners", "Resource intensive", "Slower than NoSQL for simple queries", "Configuration complexity"]'),

('MySQL', 'An open-source relational database management system based on SQL.',
 '["Easy to use", "Fast performance", "Wide adoption", "Good documentation", "Cost-effective"]',
 '["Limited advanced features", "Licensing concerns", "Less extensible", "Storage engine complexity"]'),

('MongoDB', 'A document database designed for ease of development and scaling.',
 '["Flexible schema", "Horizontal scaling", "JSON-like documents", "Good performance", "Easy to start"]',
 '["Memory usage", "Data consistency issues", "Complex queries", "Learning curve for SQL developers"]'),

('Redis', 'An in-memory data structure store used as a database, cache, and message broker.',
 '["Very fast", "Multiple data structures", "Persistence options", "Pub/Sub messaging", "Atomic operations"]',
 '["Memory limitations", "Single-threaded", "Data durability concerns", "Limited query capabilities"]'),

('Supabase', 'An open source Firebase alternative providing database, authentication, instant APIs, and realtime subscriptions.',
 '["PostgreSQL based", "Real-time subscriptions", "Built-in auth", "Auto-generated APIs", "Open source"]',
 '["Newer platform", "Limited ecosystem", "Vendor lock-in potential", "Learning curve"]'),

('Firebase', 'A platform developed by Google for creating mobile and web applications with backend services.',
 '["Real-time database", "Easy setup", "Google integration", "Scalable", "Built-in authentication"]',
 '["Vendor lock-in", "Limited querying", "Pricing can escalate", "NoSQL limitations"]'),

('PlanetScale', 'A MySQL-compatible serverless database platform built on Vitess.',
 '["Serverless scaling", "Branching workflows", "MySQL compatible", "No connection limits", "Schema migrations"]',
 '["MySQL limitations", "Newer platform", "Pricing model", "Limited advanced features"]'),

('Prisma', 'A next-generation ORM for Node.js and TypeScript that provides type safety and auto-completion.',
 '["Type safety", "Auto-generated client", "Database migrations", "Great developer experience", "Multi-database support"]',
 '["Learning curve", "Generated code size", "Performance overhead", "Limited raw SQL support"]'),

('Drizzle', 'A lightweight and performant TypeScript ORM with developer experience in focus.',
 '["TypeScript first", "Lightweight", "SQL-like syntax", "Great performance", "Type safety"]',
 '["Newer ORM", "Smaller ecosystem", "Limited documentation", "Learning curve"]'),

('NextAuth.js', 'A complete open source authentication solution for Next.js applications.',
 '["Easy integration", "Multiple providers", "Secure by default", "Customizable", "TypeScript support"]',
 '["Next.js specific", "Configuration complexity", "Limited customization", "Database requirements"]'),

('Auth0', 'A flexible, drop-in solution to add authentication and authorization services to applications.',
 '["Easy integration", "Multiple providers", "Enterprise features", "Scalable", "Good documentation"]',
 '["Pricing can be high", "Vendor lock-in", "Complex for simple use cases", "Learning curve"]'),

('Clerk', 'A complete suite of embeddable UIs, flexible APIs, and admin dashboards to authenticate and manage users.',
 '["Great developer experience", "Pre-built components", "Modern design", "Easy integration", "Good documentation"]',
 '["Newer platform", "Pricing model", "Limited customization", "Vendor lock-in"]'),

('Firebase Auth', 'Authentication service provided by Firebase with support for multiple sign-in methods.',
 '["Easy setup", "Multiple providers", "Google integration", "Scalable", "Real-time features"]',
 '["Vendor lock-in", "Limited customization", "Firebase ecosystem dependency", "Pricing concerns"]')
ON CONFLICT (name) DO NOTHING;

-- 4. Insert Technologies (Part 3 - Payments, Hosting, etc.)
INSERT INTO technologies (name, description, pros, cons) VALUES
('Stripe', 'A technology company that builds economic infrastructure for the internet.',
 '["Developer friendly", "Comprehensive APIs", "Global support", "Strong documentation", "Reliable"]',
 '["Transaction fees", "Complex pricing", "US-focused initially", "Compliance requirements"]'),

('Lemonsqueezy', 'A merchant of record that handles payments, taxes, and compliance for digital products.',
 '["Merchant of record", "Tax handling", "Easy setup", "Digital product focused", "Global compliance"]',
 '["Higher fees", "Limited customization", "Newer platform", "Less ecosystem"]'),

('PayPal', 'A digital payment platform that allows users to make payments and money transfers.',
 '["Wide acceptance", "Buyer protection", "Easy integration", "Global reach", "Brand recognition"]',
 '["High fees", "Account freezing issues", "Limited customization", "Dispute resolution"]'),

('Paddle', 'A complete payments infrastructure for SaaS companies handling billing, invoicing, and compliance.',
 '["Merchant of record", "Tax compliance", "Subscription billing", "Global reach", "Analytics"]',
 '["Higher fees", "Limited customization", "Complex setup", "Vendor lock-in"]'),

('Vercel', 'A platform for frontend frameworks and static sites, built to integrate with headless content and commerce.',
 '["Easy deployment", "Global CDN", "Automatic scaling", "Great developer experience", "Next.js integration"]',
 '["Vendor lock-in", "Pricing can escalate", "Limited backend features", "Cold starts"]'),

('Netlify', 'A platform that automates builds, deployments, and manages websites and web apps.',
 '["Easy deployment", "Continuous deployment", "Form handling", "Edge functions", "Great free tier"]',
 '["Limited backend features", "Build time limits", "Vendor lock-in", "Complex pricing"]'),

('Railway', 'A deployment platform designed to streamline the software development life-cycle.',
 '["Easy deployment", "Database included", "Auto-scaling", "Good developer experience", "Fair pricing"]',
 '["Newer platform", "Limited features", "Smaller ecosystem", "Geographic limitations"]'),

('Heroku', 'A platform as a service that enables developers to build, run, and operate applications entirely in the cloud.',
 '["Easy deployment", "Add-on ecosystem", "Automatic scaling", "Good documentation", "Mature platform"]',
 '["Expensive", "Cold starts", "Limited customization", "Vendor lock-in"]'),

('AWS', 'Amazon Web Services provides on-demand cloud computing platforms and APIs.',
 '["Comprehensive services", "Global infrastructure", "Scalable", "Mature platform", "Enterprise ready"]',
 '["Complex pricing", "Steep learning curve", "Over-engineering risk", "Vendor lock-in"]'),

('Google Cloud', 'A suite of cloud computing services that runs on the same infrastructure that Google uses internally.',
 '["Google integration", "Machine learning services", "Global network", "Competitive pricing", "Kubernetes native"]',
 '["Complex setup", "Learning curve", "Less mature than AWS", "Limited third-party integrations"]'),

('DigitalOcean', 'A cloud infrastructure provider focused on simplifying cloud computing for developers.',
 '["Simple pricing", "Developer friendly", "Good documentation", "Predictable costs", "Easy to use"]',
 '["Limited services", "Less enterprise features", "Smaller global presence", "Basic monitoring"]'),

('Cloudflare', 'A web infrastructure and website security company providing CDN and DDoS mitigation services.',
 '["Global CDN", "DDoS protection", "Free tier", "Performance optimization", "Security features"]',
 '["Complex configuration", "Limited customization", "Vendor lock-in", "Learning curve"]')
ON CONFLICT (name) DO NOTHING;

-- 5. Insert SaaS Types
INSERT INTO saas_types (key, name, description, tech_stack) VALUES
('ecommerce', 'E-commerce Platform', 'Online store with product catalog, shopping cart, and payment processing',
 '{
   "frontend": {"primary": ["Next.js", "React", "TypeScript"], "alternatives": ["Vue.js", "Angular"]},
   "backend": {"primary": ["Node.js", "Express.js"], "alternatives": ["Nest.js", "Fastify"]},
   "database": {"primary": ["PostgreSQL"], "alternatives": ["MySQL", "MongoDB"]},
   "auth": {"primary": ["NextAuth.js"], "alternatives": ["Auth0", "Clerk"]},
   "payments": {"primary": ["Stripe"], "alternatives": ["PayPal", "Lemonsqueezy"]},
   "hosting": {"primary": ["Vercel"], "alternatives": ["Netlify", "Railway"]}
 }'),

('crm', 'Customer Relationship Management', 'Manage customer interactions, sales pipeline, and business relationships',
 '{
   "frontend": {"primary": ["React", "TypeScript", "Tailwind CSS"], "alternatives": ["Vue.js", "Angular"]},
   "backend": {"primary": ["Node.js", "Nest.js"], "alternatives": ["Express.js", "Fastify"]},
   "database": {"primary": ["PostgreSQL", "Prisma"], "alternatives": ["MySQL", "Supabase"]},
   "auth": {"primary": ["Auth0"], "alternatives": ["NextAuth.js", "Clerk"]},
   "hosting": {"primary": ["Railway"], "alternatives": ["Vercel", "Heroku"]}
 }'),

('saas_starter', 'SaaS Starter Kit', 'Foundation for building Software as a Service applications',
 '{
   "frontend": {"primary": ["Next.js", "React", "TypeScript", "Tailwind CSS"], "alternatives": ["Vue.js", "Svelte"]},
   "backend": {"primary": ["Node.js", "Express.js"], "alternatives": ["Nest.js", "Fastify"]},
   "database": {"primary": ["PostgreSQL", "Drizzle"], "alternatives": ["Supabase", "PlanetScale"]},
   "auth": {"primary": ["NextAuth.js"], "alternatives": ["Clerk", "Auth0"]},
   "payments": {"primary": ["Stripe"], "alternatives": ["Lemonsqueezy", "Paddle"]},
   "hosting": {"primary": ["Vercel"], "alternatives": ["Railway", "Netlify"]}
 }'),

('marketplace', 'Marketplace Platform', 'Multi-vendor platform connecting buyers and sellers',
 '{
   "frontend": {"primary": ["Next.js", "React", "TypeScript"], "alternatives": ["Vue.js", "Angular"]},
   "backend": {"primary": ["Node.js", "Nest.js"], "alternatives": ["Express.js", "Fastify"]},
   "database": {"primary": ["PostgreSQL", "Redis"], "alternatives": ["MongoDB", "MySQL"]},
   "auth": {"primary": ["Auth0"], "alternatives": ["NextAuth.js", "Firebase Auth"]},
   "payments": {"primary": ["Stripe"], "alternatives": ["PayPal", "Paddle"]},
   "hosting": {"primary": ["AWS"], "alternatives": ["Google Cloud", "Railway"]}
 }'),

('blog', 'Blog Platform', 'Content management system for publishing articles and blog posts',
 '{
   "frontend": {"primary": ["Next.js", "React", "Tailwind CSS"], "alternatives": ["Vue.js", "Svelte"]},
   "backend": {"primary": ["Node.js", "Express.js"], "alternatives": ["Nest.js"]},
   "database": {"primary": ["PostgreSQL"], "alternatives": ["MongoDB", "Supabase"]},
   "auth": {"primary": ["NextAuth.js"], "alternatives": ["Clerk", "Auth0"]},
   "hosting": {"primary": ["Vercel"], "alternatives": ["Netlify", "Railway"]}
 }'),

('social_media', 'Social Media Platform', 'Social networking application with user profiles and content sharing',
 '{
   "frontend": {"primary": ["React", "TypeScript", "Tailwind CSS"], "alternatives": ["Vue.js", "Next.js"]},
   "backend": {"primary": ["Node.js", "Nest.js"], "alternatives": ["Express.js", "Fastify"]},
   "database": {"primary": ["PostgreSQL", "Redis"], "alternatives": ["MongoDB", "Supabase"]},
   "auth": {"primary": ["Auth0"], "alternatives": ["Firebase Auth", "Clerk"]},
   "hosting": {"primary": ["AWS"], "alternatives": ["Google Cloud", "Railway"]}
 }')
ON CONFLICT (key) DO NOTHING;

-- Success message
SELECT 'Database seeding completed successfully!' as message;