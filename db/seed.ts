import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { technologies, techCategories, saasTypes } from './schema';
import { technologyDetails, techStackData } from '../lib/techStackData';

// Initialize database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // 1. Seed Technologies
    console.log('📦 Seeding technologies...');
    const techEntries = Object.entries(technologyDetails);
    
    for (const [name, details] of techEntries) {
      await db.insert(technologies).values({
        name,
        description: details.description,
        pros: details.pros,
        cons: details.cons,
        imageUrl: null, // Will be added when you create the icons
      }).onConflictDoNothing(); // Skip if already exists
    }
    console.log(`✅ Seeded ${techEntries.length} technologies`);

    // 2. Seed Tech Categories
    console.log('🏷️ Seeding tech categories...');
    const categoryEntries = Object.entries(techStackData.techCategories);
    
    for (const [key, category] of categoryEntries) {
      await db.insert(techCategories).values({
        key,
        name: category.name,
        description: category.description,
        color: category.color,
        sortOrder: 0, // You can adjust this later if needed
      }).onConflictDoNothing();
    }
    console.log(`✅ Seeded ${categoryEntries.length} tech categories`);

    // 3. Seed SaaS Types
    console.log('🏢 Seeding SaaS types...');
    const saasTypeEntries = Object.entries(techStackData.saasTypes);
    
    for (const [key, saasType] of saasTypeEntries) {
      await db.insert(saasTypes).values({
        key,
        name: saasType.name,
        description: saasType.description,
        techStack: saasType.techStack,
      }).onConflictDoNothing();
    }
    console.log(`✅ Seeded ${saasTypeEntries.length} SaaS types`);

    console.log('🎉 Database seeding completed successfully!');
    
    // Print summary
    console.log('\n📊 Seeding Summary:');
    console.log(`- Technologies: ${techEntries.length}`);
    console.log(`- Tech Categories: ${categoryEntries.length}`);
    console.log(`- SaaS Types: ${saasTypeEntries.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✨ Seeding process finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
