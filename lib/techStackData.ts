// Technology details with descriptions, pros, and cons
export const technologyDetails = {
  // Frontend Technologies
  "Next.js": {
    description: "A React framework that enables functionality such as server-side rendering and generating static websites for React based web applications.",
    pros: ["Server-side rendering", "Static site generation", "Built-in routing", "API routes", "Excellent performance"],
    cons: ["Learning curve", "Can be overkill for simple projects", "Vendor lock-in with Vercel"]
  },
  "React": {
    description: "A JavaScript library for building user interfaces, particularly web applications with dynamic, interactive elements.",
    pros: ["Large ecosystem", "Component-based", "Virtual DOM", "Strong community", "Flexible"],
    cons: ["Steep learning curve", "Rapid changes", "JSX syntax", "Only handles UI"]
  },
  "Vue.js": {
    description: "A progressive JavaScript framework for building user interfaces and single-page applications.",
    pros: ["Easy to learn", "Great documentation", "Flexible", "Small bundle size", "Good performance"],
    cons: ["Smaller ecosystem than React", "Less job opportunities", "Language barriers in community"]
  },
  "Angular": {
    description: "A TypeScript-based open-source web application framework led by the Angular Team at Google.",
    pros: ["Full framework", "TypeScript by default", "Powerful CLI", "Enterprise-ready", "Two-way data binding"],
    cons: ["Steep learning curve", "Verbose", "Large bundle size", "Complex for small projects"]
  },
  "Svelte": {
    description: "A radical new approach to building user interfaces with a compile-time optimized framework.",
    pros: ["No virtual DOM", "Small bundle size", "Easy to learn", "Great performance", "Less boilerplate"],
    cons: ["Smaller ecosystem", "Limited tooling", "Less mature", "Fewer learning resources"]
  },
  "TypeScript": {
    description: "A strongly typed programming language that builds on JavaScript by adding static type definitions.",
    pros: ["Type safety", "Better IDE support", "Catches errors early", "Great for large projects", "Excellent tooling"],
    cons: ["Learning curve", "Compilation step", "Can be verbose", "Setup complexity"]
  },
  "Tailwind CSS": {
    description: "A utility-first CSS framework packed with classes that can be composed to build any design.",
    pros: ["Utility-first", "Highly customizable", "Small production builds", "Consistent design", "Fast development"],
    cons: ["Learning curve", "HTML can look cluttered", "Requires purging", "Less semantic"]
  },
  "Styled Components": {
    description: "A library for React and React Native that allows you to use component-level styles written with a mixture of JavaScript and CSS.",
    pros: ["CSS-in-JS", "Dynamic styling", "Automatic vendor prefixing", "No class name bugs", "Component-based"],
    cons: ["Runtime overhead", "Learning curve", "Bundle size", "Debugging complexity"]
  },
  "Zustand": {
    description: "A small, fast and scalable bearbones state-management solution using simplified flux principles.",
    pros: ["Simple API", "Small bundle size", "No boilerplate", "TypeScript support", "Framework agnostic"],
    cons: ["Less mature", "Smaller ecosystem", "Limited devtools", "Less documentation"]
  },
  "Redux Toolkit": {
    description: "The official, opinionated, batteries-included toolset for efficient Redux development.",
    pros: ["Predictable state", "Time-travel debugging", "Large ecosystem", "Well-tested patterns", "Great devtools"],
    cons: ["Boilerplate code", "Learning curve", "Can be overkill", "Complexity for simple apps"]
  },

  // Backend Technologies
  "Node.js": {
    description: "A JavaScript runtime built on Chrome's V8 JavaScript engine for building scalable network applications.",
    pros: ["JavaScript everywhere", "Large ecosystem", "Fast development", "Great for real-time apps", "NPM packages"],
    cons: ["Single-threaded", "Callback complexity", "Not ideal for CPU-intensive tasks", "Rapid changes"]
  },
  "Express.js": {
    description: "A minimal and flexible Node.js web application framework that provides robust features for web and mobile applications.",
    pros: ["Minimalist", "Flexible", "Large ecosystem", "Easy to learn", "Great middleware support"],
    cons: ["Minimal by default", "No built-in structure", "Security concerns", "Callback hell potential"]
  },
  "Python/Django": {
    description: "A high-level Python web framework that encourages rapid development and clean, pragmatic design.",
    pros: ["Batteries included", "Admin interface", "ORM", "Security features", "Rapid development"],
    cons: ["Monolithic", "Learning curve", "Performance limitations", "Less flexible"]
  },
  "Python/FastAPI": {
    description: "A modern, fast web framework for building APIs with Python 3.6+ based on standard Python type hints.",
    pros: ["Fast performance", "Automatic API docs", "Type hints", "Async support", "Easy to learn"],
    cons: ["Newer framework", "Smaller ecosystem", "Less mature", "Limited resources"]
  },
  "Ruby on Rails": {
    description: "A server-side web application framework written in Ruby under the MIT License.",
    pros: ["Convention over configuration", "Rapid development", "Great community", "Mature ecosystem", "Developer happiness"],
    cons: ["Performance concerns", "Magic can be confusing", "Monolithic", "Learning curve"]
  },
  "PHP/Laravel": {
    description: "A web application framework with expressive, elegant syntax designed to make development enjoyable and creative.",
    pros: ["Elegant syntax", "Great documentation", "Built-in features", "Large community", "Easy deployment"],
    cons: ["PHP stigma", "Performance concerns", "Monolithic", "Security concerns if not careful"]
  },
  "Java/Spring": {
    description: "A comprehensive programming and configuration model for modern Java-based enterprise applications.",
    pros: ["Enterprise-ready", "Mature ecosystem", "Strong typing", "Great tooling", "Scalable"],
    cons: ["Verbose", "Complex configuration", "Learning curve", "Heavy framework"]
  },
  "GraphQL": {
    description: "A query language for APIs and a runtime for fulfilling those queries with existing data.",
    pros: ["Single endpoint", "Type system", "Efficient data fetching", "Strong tooling", "Self-documenting"],
    cons: ["Learning curve", "Caching complexity", "Over-fetching potential", "N+1 query problem"]
  },

  // Database Technologies
  "PostgreSQL": {
    description: "A powerful, open source object-relational database system with strong reputation for reliability and performance.",
    pros: ["ACID compliance", "Advanced features", "Extensible", "Strong consistency", "Great performance"],
    cons: ["Memory usage", "Complexity", "Learning curve", "Slower for simple queries"]
  },
  "MySQL": {
    description: "An open-source relational database management system based on SQL – Structured Query Language.",
    pros: ["Easy to use", "Fast", "Reliable", "Large community", "Good documentation"],
    cons: ["Limited features", "Storage engine complexity", "Replication lag", "Less advanced than PostgreSQL"]
  },
  "MongoDB": {
    description: "A source-available cross-platform document-oriented database program classified as a NoSQL database.",
    pros: ["Flexible schema", "Horizontal scaling", "JSON-like documents", "Fast development", "Good for unstructured data"],
    cons: ["Memory usage", "Data consistency", "Complex queries", "Learning curve"]
  },
  "Redis": {
    description: "An in-memory data structure store, used as a database, cache, and message broker.",
    pros: ["Very fast", "Versatile data structures", "Pub/Sub messaging", "Atomic operations", "Great for caching"],
    cons: ["Memory-only", "Data persistence complexity", "Single-threaded", "Memory limitations"]
  },
  "Supabase": {
    description: "An open source Firebase alternative providing a Postgres database, Authentication, instant APIs, and Realtime subscriptions.",
    pros: ["Open source", "PostgreSQL-based", "Real-time features", "Built-in auth", "Easy to use"],
    cons: ["Newer platform", "Limited customization", "Vendor lock-in", "Less mature ecosystem"]
  },

  // Authentication
  "NextAuth.js": {
    description: "A complete open source authentication solution for Next.js applications.",
    pros: ["Easy integration", "Multiple providers", "Secure by default", "TypeScript support", "Flexible"],
    cons: ["Next.js specific", "Limited customization", "Database sessions complexity", "Learning curve"]
  },
  "Auth0": {
    description: "A flexible, drop-in solution to add authentication and authorization services to applications.",
    pros: ["Enterprise features", "Multiple protocols", "Easy integration", "Scalable", "Great documentation"],
    cons: ["Pricing", "Vendor lock-in", "Complexity for simple use cases", "Limited customization"]
  },
  "Firebase Auth": {
    description: "A service that can authenticate users using only client-side code, handling the backend automatically.",
    pros: ["Easy setup", "Multiple providers", "Real-time", "Scalable", "Google integration"],
    cons: ["Vendor lock-in", "Limited customization", "Pricing model", "Google dependency"]
  },
  "Clerk": {
    description: "A complete suite of embeddable UIs, flexible APIs, and admin dashboards to authenticate and manage users.",
    pros: ["Modern UI", "Easy integration", "Great DX", "Comprehensive features", "Good documentation"],
    cons: ["Newer platform", "Pricing", "Limited customization", "Vendor lock-in"]
  },

  // Deployment
  "Vercel": {
    description: "A cloud platform for static sites and Serverless Functions that fits perfectly with your workflow.",
    pros: ["Easy deployment", "Great DX", "Edge network", "Automatic scaling", "Git integration"],
    cons: ["Vendor lock-in", "Pricing for scale", "Limited backend features", "Cold starts"]
  },
  "Netlify": {
    description: "A web developer platform that multiplies productivity by unifying the elements of the modern decoupled web.",
    pros: ["Easy deployment", "Great for static sites", "Built-in CDN", "Form handling", "Split testing"],
    cons: ["Limited backend", "Function limitations", "Pricing for scale", "Build time limits"]
  },
  "AWS": {
    description: "Amazon Web Services offers reliable, scalable, and inexpensive cloud computing services.",
    pros: ["Comprehensive services", "Scalable", "Reliable", "Global infrastructure", "Enterprise-ready"],
    cons: ["Complex", "Learning curve", "Pricing complexity", "Vendor lock-in"]
  },
  "Google Cloud": {
    description: "A suite of cloud computing services that runs on the same infrastructure that Google uses internally.",
    pros: ["Good pricing", "Strong AI/ML services", "Kubernetes native", "Global network", "Innovation"],
    cons: ["Smaller ecosystem", "Less mature", "Learning curve", "Service discontinuation risk"]
  },
  "DigitalOcean": {
    description: "A cloud infrastructure provider focused on simplifying web infrastructure for software developers.",
    pros: ["Simple pricing", "Easy to use", "Good documentation", "Developer-friendly", "Predictable costs"],
    cons: ["Limited services", "Less enterprise features", "Smaller scale", "Limited global presence"]
  },

  // Additional Technologies
  "Material-UI": {
    description: "A popular React UI framework that implements Google's Material Design principles.",
    pros: ["Comprehensive components", "Material Design", "Good documentation", "Active community", "Theming support"],
    cons: ["Large bundle size", "Opinionated design", "Learning curve", "Customization complexity"]
  },
  "Ant Design": {
    description: "An enterprise-class UI design language and React UI library with high-quality components.",
    pros: ["Enterprise-ready", "Comprehensive components", "Good design system", "TypeScript support", "Internationalization"],
    cons: ["Large bundle size", "Opinionated styling", "Chinese-focused", "Customization difficulty"]
  },
  "Framer Motion": {
    description: "A production-ready motion library for React that makes creating animations simple.",
    pros: ["Easy animations", "Declarative API", "Performance optimized", "Gesture support", "Layout animations"],
    cons: ["Bundle size", "Learning curve", "React-specific", "Complex animations can be tricky"]
  },
  "Prisma": {
    description: "A next-generation ORM that helps developers build faster and make fewer errors with an auto-generated query builder.",
    pros: ["Type safety", "Auto-generated client", "Database migrations", "Great DX", "Multi-database support"],
    cons: ["Learning curve", "Vendor lock-in", "Performance overhead", "Limited raw SQL"]
  },
  "PlanetScale": {
    description: "A MySQL-compatible serverless database platform built for developers.",
    pros: ["Serverless scaling", "Branching workflows", "No connection limits", "Built-in analytics", "Easy scaling"],
    cons: ["MySQL only", "Pricing model", "Newer platform", "Limited customization"]
  },
  "Railway": {
    description: "A deployment platform designed to streamline the software development life-cycle.",
    pros: ["Easy deployment", "Git integration", "Multiple languages", "Database hosting", "Simple pricing"],
    cons: ["Limited features", "Newer platform", "Less enterprise features", "Resource limitations"]
  },
  "Mailgun": {
    description: "A set of powerful APIs that enable you to send, receive and track email effortlessly.",
    pros: ["Reliable delivery", "Good API", "Analytics", "Flexible pricing", "Developer-friendly"],
    cons: ["Complex pricing", "Learning curve", "Limited templates", "Deliverability challenges"]
  },
  "AWS SES": {
    description: "Amazon Simple Email Service is a cost-effective, flexible, and scalable email service.",
    pros: ["Cost-effective", "Scalable", "AWS integration", "High deliverability", "Flexible"],
    cons: ["Complex setup", "AWS complexity", "Limited features", "Reputation management"]
  },
  "Okta": {
    description: "A leading provider of identity and access management solutions for the enterprise.",
    pros: ["Enterprise features", "SSO support", "Compliance", "Scalable", "Comprehensive"],
    cons: ["Expensive", "Complex setup", "Overkill for small apps", "Learning curve"]
  },
  "AWS Cognito": {
    description: "Amazon Cognito provides authentication, authorization, and user management for web and mobile apps.",
    pros: ["AWS integration", "Scalable", "Comprehensive features", "Cost-effective", "Secure"],
    cons: ["AWS complexity", "Learning curve", "Limited customization", "Vendor lock-in"]
  },
  "Pusher": {
    description: "Pusher provides realtime communication APIs to connect, engage, and grow your applications.",
    pros: ["Easy integration", "Reliable", "Good documentation", "Multiple platforms", "Real-time features"],
    cons: ["Pricing model", "Vendor lock-in", "Limited customization", "Connection limits"]
  },
  "Ably": {
    description: "A realtime experience infrastructure platform providing APIs for live chat, data synchronization, and notifications.",
    pros: ["Reliable infrastructure", "Global edge network", "Comprehensive features", "Good documentation", "Scalable"],
    cons: ["Pricing complexity", "Learning curve", "Vendor lock-in", "Overkill for simple use cases"]
  },
  "ClickHouse": {
    description: "An open-source column-oriented database management system for online analytical processing.",
    pros: ["Very fast queries", "Column-oriented", "Scalable", "Open source", "Great for analytics"],
    cons: ["Complex setup", "Learning curve", "Limited ACID", "Memory usage"]
  },
  "BigQuery": {
    description: "Google's fully managed, petabyte-scale, and cost-effective analytics data warehouse.",
    pros: ["Serverless", "Petabyte scale", "SQL interface", "Machine learning", "Cost-effective"],
    cons: ["Google lock-in", "Complex pricing", "Learning curve", "Data transfer costs"]
  },
  "Snowflake": {
    description: "A cloud-based data platform that provides data warehouse-as-a-service designed for the cloud.",
    pros: ["Cloud-native", "Scalable", "Multi-cloud", "Easy to use", "Performance"],
    cons: ["Expensive", "Vendor lock-in", "Complex pricing", "Learning curve"]
  },
  "Apache Kafka": {
    description: "An open-source distributed event streaming platform for high-performance data pipelines and streaming analytics.",
    pros: ["High throughput", "Fault tolerant", "Scalable", "Real-time processing", "Durable"],
    cons: ["Complex setup", "Resource intensive", "Learning curve", "Operational overhead"]
  },
  "Apache Airflow": {
    description: "An open-source platform to develop, schedule, and monitor workflows programmatically.",
    pros: ["Workflow management", "Extensible", "Python-based", "Rich UI", "Community"],
    cons: ["Complex setup", "Resource heavy", "Learning curve", "Maintenance overhead"]
  },
  "RabbitMQ": {
    description: "An open-source message-broker software that originally implemented the Advanced Message Queuing Protocol.",
    pros: ["Reliable messaging", "Flexible routing", "Clustering", "Management UI", "Multi-protocol"],
    cons: ["Complex configuration", "Performance limitations", "Memory usage", "Learning curve"]
  },
  "Celery": {
    description: "An asynchronous task queue/job queue based on distributed message passing for Python.",
    pros: ["Python integration", "Distributed", "Flexible", "Monitoring", "Scalable"],
    cons: ["Python-specific", "Complex setup", "Broker dependency", "Debugging difficulty"]
  },
  "DataDog": {
    description: "A monitoring and analytics platform for developers, IT operations teams, and business users.",
    pros: ["Comprehensive monitoring", "Great dashboards", "APM features", "Integrations", "Real-time alerts"],
    cons: ["Expensive", "Complex pricing", "Learning curve", "Data retention limits"]
  },
  "New Relic": {
    description: "A comprehensive observability platform that helps engineers create more perfect software.",
    pros: ["Full-stack monitoring", "APM", "Real user monitoring", "AI insights", "Good documentation"],
    cons: ["Expensive", "Complex interface", "Data sampling", "Learning curve"]
  },
  "Grafana": {
    description: "An open-source platform for monitoring and observability with beautiful dashboards.",
    pros: ["Open source", "Beautiful dashboards", "Multiple data sources", "Alerting", "Customizable"],
    cons: ["Setup complexity", "Learning curve", "Performance with large datasets", "Maintenance"]
  },
  "Prometheus": {
    description: "An open-source monitoring system with a dimensional data model and flexible query language.",
    pros: ["Time-series database", "Powerful queries", "Service discovery", "Alerting", "Open source"],
    cons: ["Storage limitations", "Complex setup", "Learning curve", "Resource usage"]
  },
  "Stripe Connect": {
    description: "A set of programmable APIs and tools that lets you route payments between multiple parties.",
    pros: ["Multi-party payments", "Comprehensive features", "Good documentation", "Reliable", "Global support"],
    cons: ["Complex implementation", "Fees structure", "Compliance requirements", "Learning curve"]
  },
  "PayPal Marketplace": {
    description: "PayPal's solution for marketplace and platform businesses to facilitate payments between buyers and sellers.",
    pros: ["Brand recognition", "Global reach", "Buyer protection", "Easy integration", "Multiple currencies"],
    cons: ["High fees", "Account holds", "Limited customization", "Dispute process"]
  },
  "Adyen": {
    description: "A global payment company that allows businesses to accept e-commerce, mobile, and point-of-sale payments.",
    pros: ["Global coverage", "Single integration", "Advanced features", "Reporting", "Reliability"],
    cons: ["Complex pricing", "Enterprise-focused", "Learning curve", "Minimum volumes"]
  },
  "Elasticsearch": {
    description: "A distributed, RESTful search and analytics engine capable of addressing a growing number of use cases.",
    pros: ["Powerful search", "Real-time", "Scalable", "Analytics", "Full-text search"],
    cons: ["Resource intensive", "Complex setup", "Memory usage", "Learning curve"]
  },
  "Algolia": {
    description: "A hosted search engine capable of delivering real-time results from the first keystroke.",
    pros: ["Fast search", "Easy integration", "Real-time", "Analytics", "Good documentation"],
    cons: ["Pricing model", "Vendor lock-in", "Limited customization", "Record limits"]
  },
  "Solr": {
    description: "An open-source enterprise-search platform, written in Java, from the Apache Lucene project.",
    pros: ["Open source", "Powerful features", "Faceted search", "Scalable", "Mature"],
    cons: ["Complex configuration", "Java dependency", "Learning curve", "Resource usage"]
  },
  "Typesense": {
    description: "A fast, typo-tolerant search engine optimized for instant search experiences.",
    pros: ["Fast performance", "Typo tolerance", "Easy setup", "Open source", "Good documentation"],
    cons: ["Newer platform", "Limited features", "Smaller community", "Less mature"]
  },
  "Socket.io": {
    description: "A library that enables real-time bidirectional event-based communication between web clients and servers.",
    pros: ["Real-time communication", "Fallback support", "Easy to use", "Cross-platform", "Active community"],
    cons: ["Performance overhead", "Scaling challenges", "WebSocket dependency", "Memory usage"]
  },
  "Redis Pub/Sub": {
    description: "Redis Pub/Sub implements the messaging system where senders send messages while receivers receive them.",
    pros: ["Very fast", "Simple to use", "Reliable", "Pattern matching", "Lightweight"],
    cons: ["No persistence", "Memory-only", "Limited features", "Single-threaded"]
  },
  "AWS SNS": {
    description: "Amazon Simple Notification Service is a fully managed messaging service for both application-to-application and application-to-person communication.",
    pros: ["Fully managed", "Scalable", "Multiple protocols", "AWS integration", "Cost-effective"],
    cons: ["AWS lock-in", "Limited features", "Message size limits", "Complexity"]
  },
  "Lemonsqueezy": {
    description: "An all-in-one platform for running your SaaS business, handling payments, subscriptions, and more.",
    pros: ["SaaS-focused", "Easy setup", "Tax handling", "Subscription management", "Good documentation"],
    cons: ["Limited customization", "Newer platform", "Pricing model", "Feature limitations"]
  },
  "Paddle": {
    description: "A complete payments infrastructure for SaaS companies, handling billing, invoicing, and tax compliance.",
    pros: ["Tax compliance", "Global payments", "Subscription billing", "Fraud protection", "Analytics"],
    cons: ["Higher fees", "Limited customization", "Approval process", "Learning curve"]
  },
  "Azure": {
    description: "Microsoft's cloud computing platform offering a wide range of cloud services including computing, analytics, storage and networking.",
    pros: ["Enterprise integration", "Hybrid cloud", "Microsoft ecosystem", "Compliance", "Global presence"],
    cons: ["Complex pricing", "Learning curve", "Microsoft dependency", "Interface complexity"]
  },
  "Azure Blob": {
    description: "Microsoft's object storage solution for the cloud, optimized for storing massive amounts of unstructured data.",
    pros: ["Scalable storage", "Cost-effective", "Azure integration", "Multiple access tiers", "Security"],
    cons: ["Azure lock-in", "Complex pricing", "Learning curve", "Performance variations"]
  },
  "Google Cloud Storage": {
    description: "A RESTful online file storage web service for storing and accessing data on Google Cloud Platform infrastructure.",
    pros: ["Global infrastructure", "Cost-effective", "Integration", "Performance", "Security"],
    cons: ["Google lock-in", "Complex pricing", "Learning curve", "Data transfer costs"]
  },
  "Cloudinary": {
    description: "A cloud-based image and video management service providing a comprehensive API for uploading, storing, managing, manipulating, and delivering images and video.",
    pros: ["Image optimization", "CDN delivery", "Transformations", "Easy integration", "Good documentation"],
    cons: ["Pricing model", "Vendor lock-in", "Limited free tier", "Complex features"]
  },
  "Square": {
    description: "A financial services platform providing payment processing, point-of-sale solutions, and business services.",
    pros: ["Easy integration", "In-person payments", "Transparent pricing", "Good support", "Comprehensive features"],
    cons: ["US-focused", "Limited international", "Feature limitations", "Account holds"]
  },
  "Braintree": {
    description: "A full-stack payment platform owned by PayPal that makes it easy to accept payments in your app or website.",
    pros: ["PayPal integration", "Global reach", "Developer-friendly", "Vault security", "Multiple payment methods"],
    cons: ["PayPal dependency", "Complex pricing", "Limited customization", "Account approval"]
  },
  "C#/.NET": {
    description: "A general-purpose, multi-paradigm programming language and a free, cross-platform, open-source developer platform.",
    pros: ["Strong typing", "Performance", "Enterprise-ready", "Microsoft ecosystem", "Cross-platform"],
    cons: ["Microsoft dependency", "Learning curve", "Verbose syntax", "Licensing costs"]
  },
  "R/Shiny": {
    description: "An R package that makes it easy to build interactive web applications straight from R.",
    pros: ["Statistical computing", "Data visualization", "R ecosystem", "Interactive dashboards", "Academic support"],
    cons: ["R-specific", "Performance limitations", "Learning curve", "Deployment complexity"]
  },
  "Scala/Spark": {
    description: "A general-purpose programming language providing support for both object-oriented and functional programming, often used with Apache Spark.",
    pros: ["Big data processing", "Functional programming", "JVM compatibility", "Scalable", "Performance"],
    cons: ["Complex syntax", "Learning curve", "Compilation time", "Smaller community"]
  },
  "JWT": {
    description: "JSON Web Tokens are an open, industry standard method for representing claims securely between two parties.",
    pros: ["Stateless", "Compact", "Self-contained", "Cross-domain", "Standard"],
    cons: ["Token size", "No revocation", "Security concerns", "Complexity"]
  },
  "Docker": {
    description: "A set of platform as a service products that use OS-level virtualization to deliver software in packages called containers.",
    pros: ["Containerization", "Portability", "Consistency", "Scalability", "DevOps integration"],
    cons: ["Learning curve", "Resource overhead", "Security concerns", "Complexity"]
  },
  "Kubernetes": {
    description: "An open-source container orchestration system for automating software deployment, scaling, and management.",
    pros: ["Container orchestration", "Auto-scaling", "Self-healing", "Portable", "Industry standard"],
    cons: ["Complex setup", "Learning curve", "Resource overhead", "Operational complexity"]
  },
  "Pandas": {
    description: "A fast, powerful, flexible and easy to use open source data analysis and manipulation tool, built on top of NumPy.",
    pros: ["Data manipulation", "Easy to use", "Powerful features", "Python integration", "Large community"],
    cons: ["Memory usage", "Performance limitations", "Learning curve", "Python-specific"]
  },
  "Chart.js": {
    description: "Simple yet flexible JavaScript charting library for designers and developers.",
    pros: ["Easy to use", "Responsive", "Lightweight", "Good documentation", "Customizable"],
    cons: ["Limited chart types", "Performance with large datasets", "Basic animations", "Canvas-based"]
  },
  "D3.js": {
    description: "A JavaScript library for producing dynamic, interactive data visualizations in web browsers using SVG, HTML and CSS.",
    pros: ["Powerful visualizations", "Highly customizable", "SVG-based", "Large community", "Flexible"],
    cons: ["Steep learning curve", "Complex for simple charts", "Performance considerations", "Verbose code"]
  },
  "WebSockets": {
    description: "A computer communications protocol, providing full-duplex communication channels over a single TCP connection.",
    pros: ["Real-time communication", "Low latency", "Bidirectional", "Efficient", "Standard protocol"],
    cons: ["Connection management", "Scaling challenges", "Firewall issues", "Complexity"]
  },
  "Multi-tenant setup": {
    description: "An architecture pattern where a single instance of software serves multiple tenants with data isolation.",
    pros: ["Resource efficiency", "Cost-effective", "Easier maintenance", "Scalable", "Centralized updates"],
    cons: ["Security complexity", "Data isolation challenges", "Customization limitations", "Performance impact"]
  },
  "Split payments": {
    description: "A payment processing feature that allows funds to be automatically distributed among multiple recipients.",
    pros: ["Automated distribution", "Marketplace support", "Reduced complexity", "Real-time processing", "Compliance"],
    cons: ["Implementation complexity", "Fee structures", "Regulatory requirements", "Limited flexibility"]
  },
  "Next.js API Routes": {
    description: "A feature of Next.js that allows you to build API endpoints as part of your Next.js application.",
    pros: ["Integrated with Next.js", "Serverless functions", "Easy deployment", "TypeScript support", "File-based routing"],
    cons: ["Vendor lock-in", "Limited to Next.js", "Cold starts", "Scaling limitations"]
  },
  "Resend": {
    description: "A modern email API built for developers, designed to be simple, reliable, and developer-friendly.",
    pros: ["Developer-focused", "Simple API", "Good deliverability", "Modern design", "Easy integration"],
    cons: ["Newer platform", "Limited features", "Pricing model", "Smaller ecosystem"]
  },
  "SendGrid": {
    description: "A cloud-based SMTP provider that allows you to send email without having to maintain email servers.",
    pros: ["Reliable delivery", "Scalable", "Good API", "Analytics", "Template engine"],
    cons: ["Complex pricing", "Account restrictions", "Learning curve", "Deliverability issues"]
  }
};

export const techStackData = {
  saasTypes: {
    ecommerce: {
      name: "E-commerce Platform",
      description: "Online store with product catalog, shopping cart, and payment processing",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Styled Components"],
          stateManagement: ["Zustand", "Redux Toolkit"],
          alternatives: ["Vue.js", "Angular", "Svelte"]
        },
        backend: {
          primary: ["Node.js", "Express.js"],
          alternatives: ["Python/Django", "Ruby on Rails", "PHP/Laravel", "Java/Spring"]
        },
        database: {
          primary: ["PostgreSQL"],
          alternatives: ["MySQL", "MongoDB", "Supabase"]
        },
        auth: {
          primary: ["NextAuth.js", "Auth0"],
          alternatives: ["Firebase Auth", "Supabase Auth", "Clerk"]
        },
        payments: {
          primary: ["Stripe", "PayPal"],
          alternatives: ["Square", "Braintree"]
        },
        deployment: {
          primary: ["Vercel", "Netlify"],
          alternatives: ["AWS", "Google Cloud", "DigitalOcean"]
        },
        storage: {
          primary: ["AWS S3", "Cloudinary"],
          alternatives: ["Google Cloud Storage", "Azure Blob"]
        }
      }
    },
    crm: {
      name: "Customer Relationship Management",
      description: "Manage customer interactions, sales pipeline, and business relationships",
      techStack: {
        frontend: {
          primary: ["React", "TypeScript", "Next.js"],
          styling: ["Tailwind CSS", "Material-UI"],
          stateManagement: ["React Query", "Zustand"],
          alternatives: ["Vue.js", "Angular"]
        },
        backend: {
          primary: ["Node.js", "Express.js", "GraphQL"],
          alternatives: ["Python/FastAPI", "C#/.NET", "Java/Spring"]
        },
        database: {
          primary: ["PostgreSQL", "Redis"],
          alternatives: ["MySQL", "MongoDB"]
        },
        auth: {
          primary: ["Auth0", "Firebase Auth"],
          alternatives: ["Okta", "AWS Cognito"]
        },
        realtime: {
          primary: ["Socket.io", "WebSockets"],
          alternatives: ["Pusher", "Ably"]
        },
        deployment: {
          primary: ["AWS", "Docker"],
          alternatives: ["Google Cloud", "Azure"]
        },
        analytics: {
          primary: ["Mixpanel", "Amplitude"],
          alternatives: ["Google Analytics", "PostHog"]
        }
      }
    },
    saas_starter: {
      name: "SaaS Starter Kit",
      description: "Basic SaaS application with user management and subscription billing",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Shadcn/ui"],
          stateManagement: ["Zustand", "React Query"],
          alternatives: ["Vue.js", "Svelte"]
        },
        backend: {
          primary: ["Next.js API Routes", "Prisma"],
          alternatives: ["Node.js/Express", "Supabase", "Firebase"]
        },
        database: {
          primary: ["PostgreSQL", "Supabase"],
          alternatives: ["MySQL", "PlanetScale"]
        },
        auth: {
          primary: ["NextAuth.js", "Clerk"],
          alternatives: ["Supabase Auth", "Auth0"]
        },
        payments: {
          primary: ["Stripe", "Lemonsqueezy"],
          alternatives: ["Paddle", "PayPal"]
        },
        deployment: {
          primary: ["Vercel", "Railway"],
          alternatives: ["Netlify", "AWS"]
        },
        email: {
          primary: ["Resend", "SendGrid"],
          alternatives: ["Mailgun", "AWS SES"]
        }
      }
    },
    marketplace: {
      name: "Marketplace Platform",
      description: "Multi-vendor platform connecting buyers and sellers",
      techStack: {
        frontend: {
          primary: ["Next.js", "React", "TypeScript"],
          styling: ["Tailwind CSS", "Framer Motion"],
          stateManagement: ["Zustand", "React Query"],
          alternatives: ["Vue.js", "Angular"]
        },
        backend: {
          primary: ["Node.js", "Express.js", "GraphQL"],
          alternatives: ["Python/Django", "Ruby on Rails"]
        },
        database: {
          primary: ["PostgreSQL", "Redis", "Elasticsearch"],
          alternatives: ["MongoDB", "MySQL"]
        },
        auth: {
          primary: ["Auth0", "Multi-tenant setup"],
          alternatives: ["Firebase Auth", "Okta"]
        },
        payments: {
          primary: ["Stripe Connect", "Split payments"],
          alternatives: ["PayPal Marketplace", "Adyen"]
        },
        search: {
          primary: ["Elasticsearch", "Algolia"],
          alternatives: ["Solr", "Typesense"]
        },
        deployment: {
          primary: ["AWS", "Docker", "Kubernetes"],
          alternatives: ["Google Cloud", "Azure"]
        },
        messaging: {
          primary: ["Socket.io", "Redis Pub/Sub"],
          alternatives: ["Pusher", "AWS SNS"]
        }
      }
    }
  },
  techCategories: {
    frontend: {
      name: "Frontend",
      color: "#3B82F6",
      description: "User interface and client-side logic"
    },
    backend: {
      name: "Backend",
      color: "#10B981",
      description: "Server-side logic and APIs"
    },
    database: {
      name: "Database",
      color: "#F59E0B",
      description: "Data storage and management"
    },
    auth: {
      name: "Authentication",
      color: "#EF4444",
      description: "User authentication and authorization"
    },
    payments: {
      name: "Payments",
      color: "#8B5CF6",
      description: "Payment processing and billing"
    },
    deployment: {
      name: "Deployment",
      color: "#06B6D4",
      description: "Hosting and infrastructure"
    },
    storage: {
      name: "Storage",
      color: "#84CC16",
      description: "File and media storage"
    },
    analytics: {
      name: "Analytics",
      color: "#F97316",
      description: "Data tracking and insights"
    },
    realtime: {
      name: "Real-time",
      color: "#EC4899",
      description: "Live updates and messaging"
    },
    search: {
      name: "Search",
      color: "#6366F1",
      description: "Search and discovery features"
    },
    email: {
      name: "Email",
      color: "#14B8A6",
      description: "Email delivery and notifications"
    },
    monitoring: {
      name: "Monitoring",
      color: "#64748B",
      description: "Application monitoring and logging"
    },
    dataProcessing: {
      name: "Data Processing",
      color: "#DC2626",
      description: "Data pipelines and ETL"
    },
    messaging: {
      name: "Messaging",
      color: "#7C3AED",
      description: "Communication and notifications"
    }
  }
};
