import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Types for tech stack state
export interface TechStack {
  [category: string]: {
    primary: string[];
    alternatives?: string[];
    [key: string]: any;
  };
}

export interface SelectedCategory {
  key: string;
  name: string;
  color: string;
  description: string;
  primary: string[];
  alternatives: string[];
}

export interface TechItem {
  id: string;
  name: string;
  type: 'primary' | 'alternative';
}

export interface TechStackState {
  // Selected category for modal
  selectedCategory: SelectedCategory | null;

  // Modal state for tech category reordering
  modalTechnologies: TechItem[];

  // Actions
  setSelectedCategory: (category: SelectedCategory | null) => void;

  // Modal actions
  setModalTechnologies: (technologies: TechItem[]) => void;
  initializeModalTechnologies: (primary: string[], alternatives: string[]) => void;
  reorderModalTechnologies: (technologies: TechItem[]) => void;

  resetTechStackState: () => void;
}

export const useTechStackStore = create<TechStackState>()(
  devtools(
    (set, get) => ({
      // Initial state
      selectedCategory: null,
      modalTechnologies: [],

      // Actions
      setSelectedCategory: (category) =>
        set({ selectedCategory: category }, false, 'setSelectedCategory'),

      // Modal actions
      setModalTechnologies: (technologies) => 
        set({ modalTechnologies: technologies }, false, 'setModalTechnologies'),

      initializeModalTechnologies: (primary, alternatives) => {
        const technologies: TechItem[] = [
          ...primary.map((tech, index) => ({
            id: `primary-${index}`,
            name: tech,
            type: 'primary' as const,
          })),
          ...alternatives.map((tech, index) => ({
            id: `alt-${index}`,
            name: tech,
            type: 'alternative' as const,
          })),
        ];
        set({ modalTechnologies: technologies }, false, 'initializeModalTechnologies');
      },

      reorderModalTechnologies: (technologies) => 
        set({ modalTechnologies: technologies }, false, 'reorderModalTechnologies'),

      resetTechStackState: () =>
        set({
          selectedCategory: null,
          modalTechnologies: [],
        }, false, 'resetTechStackState'),
    }),
    {
      name: 'Tech Stack Store',
    }
  )
);
