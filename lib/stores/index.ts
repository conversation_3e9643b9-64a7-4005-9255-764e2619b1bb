// Export all stores from a central location
export { useAppStore, type AppState, type SaasType } from './appStore';
export { useUIStore, type UIState } from './uiStore';
export { useTechStackStore, type TechStackState, type TechStack, type SelectedCategory, type TechItem } from './techStackStore';

// Combined store hook for components that need multiple stores
export const useStores = () => ({
  app: useAppStore(),
  ui: useUIStore(),
  techStack: useTechStackStore(),
});
