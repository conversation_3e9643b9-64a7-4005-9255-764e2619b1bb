import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Types for the main app state
export interface SaasType {
  id: string;
  name: string;
  description: string;
}

export interface CustomProject {
  id: string;
  name: string;
  description: string;
  techStack: {
    [category: string]: {
      primary: string[];
      alternatives: string[];
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface AppState {
  // Main app state
  selectedSaasType: string | null;
  isLoading: boolean;

  // Custom projects state
  isCustomProject: boolean;
  currentProject: CustomProject | null;
  customProjects: CustomProject[];

  // Actions
  setSelectedSaasType: (saasType: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  handleSaasTypeSelect: (typeId: string) => Promise<void>;

  // Custom project actions
  createNewProject: (name: string, description: string) => void;
  setCurrentProject: (project: CustomProject | null) => void;
  updateCurrentProject: (updates: Partial<CustomProject>) => void;
  addTechnologyToProject: (category: string, technology: string, isPrimary: boolean) => void;
  removeTechnologyFromProject: (category: string, technology: string) => void;
  saveProject: () => void;
  deleteProject: (projectId: string) => void;

  resetApp: () => void;
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        selectedSaasType: null,
        isLoading: false,
        isCustomProject: false,
        currentProject: null,
        customProjects: [],

        // Actions
        setSelectedSaasType: (saasType) =>
          set({
            selectedSaasType: saasType,
            isCustomProject: false,
            currentProject: null
          }, false, 'setSelectedSaasType'),

        setIsLoading: (loading) =>
          set({ isLoading: loading }, false, 'setIsLoading'),

        handleSaasTypeSelect: async (typeId: string) => {
          set({ isLoading: true }, false, 'handleSaasTypeSelect/start');

          // Simulate loading time for better UX
          await new Promise(resolve => setTimeout(resolve, 500));

          set({
            selectedSaasType: typeId,
            isLoading: false,
            isCustomProject: false,
            currentProject: null
          }, false, 'handleSaasTypeSelect/complete');
        },

        // Custom project actions
        createNewProject: (name: string, description: string) => {
          const newProject: CustomProject = {
            id: `custom-${Date.now()}`,
            name,
            description,
            techStack: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          set({
            isCustomProject: true,
            currentProject: newProject,
            selectedSaasType: null,
            customProjects: [...get().customProjects, newProject]
          }, false, 'createNewProject');
        },

        setCurrentProject: (project) =>
          set({
            currentProject: project,
            isCustomProject: !!project,
            selectedSaasType: null
          }, false, 'setCurrentProject'),

        updateCurrentProject: (updates) => {
          const current = get().currentProject;
          if (!current) return;

          const updatedProject = {
            ...current,
            ...updates,
            updatedAt: new Date()
          };

          set({
            currentProject: updatedProject,
            customProjects: get().customProjects.map(p =>
              p.id === current.id ? updatedProject : p
            )
          }, false, 'updateCurrentProject');
        },

        addTechnologyToProject: (category: string, technology: string, isPrimary: boolean) => {
          const current = get().currentProject;
          if (!current) return;

          const updatedTechStack = { ...current.techStack };
          if (!updatedTechStack[category]) {
            updatedTechStack[category] = { primary: [], alternatives: [] };
          }

          const targetArray = isPrimary ? 'primary' : 'alternatives';
          const otherArray = isPrimary ? 'alternatives' : 'primary';

          // Remove from other array if it exists there
          updatedTechStack[category][otherArray] = updatedTechStack[category][otherArray].filter(
            tech => tech !== technology
          );

          // Add to target array if not already there
          if (!updatedTechStack[category][targetArray].includes(technology)) {
            updatedTechStack[category][targetArray].push(technology);
          }

          get().updateCurrentProject({ techStack: updatedTechStack });
        },

        removeTechnologyFromProject: (category: string, technology: string) => {
          const current = get().currentProject;
          if (!current) return;

          const updatedTechStack = { ...current.techStack };
          if (updatedTechStack[category]) {
            updatedTechStack[category].primary = updatedTechStack[category].primary.filter(
              tech => tech !== technology
            );
            updatedTechStack[category].alternatives = updatedTechStack[category].alternatives.filter(
              tech => tech !== technology
            );

            // Remove category if empty
            if (updatedTechStack[category].primary.length === 0 &&
                updatedTechStack[category].alternatives.length === 0) {
              delete updatedTechStack[category];
            }
          }

          get().updateCurrentProject({ techStack: updatedTechStack });
        },

        saveProject: () => {
          const current = get().currentProject;
          if (!current) return;

          // Project is automatically saved when updated, but this can trigger additional logic
          console.log('Project saved:', current.name);
        },

        deleteProject: (projectId: string) => {
          const state = get();
          const updatedProjects = state.customProjects.filter(p => p.id !== projectId);

          set({
            customProjects: updatedProjects,
            currentProject: state.currentProject?.id === projectId ? null : state.currentProject,
            isCustomProject: state.currentProject?.id === projectId ? false : state.isCustomProject
          }, false, 'deleteProject');
        },

        resetApp: () =>
          set({
            selectedSaasType: null,
            isLoading: false,
            isCustomProject: false,
            currentProject: null
          }, false, 'resetApp'),
      }),
      {
        name: 'saas-app-store',
        partialize: (state) => ({
          selectedSaasType: state.selectedSaasType,
          customProjects: state.customProjects,
          currentProject: state.currentProject,
          isCustomProject: state.isCustomProject
        }),
      }
    ),
    {
      name: 'SaaS App Store',
    }
  )
);
