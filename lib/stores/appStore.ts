import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Types for the main app state
export interface SaasType {
  id: string;
  name: string;
  description: string;
}

export interface AppState {
  // Main app state
  selectedSaasType: string | null;
  isLoading: boolean;
  
  // Actions
  setSelectedSaasType: (saasType: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  handleSaasTypeSelect: (typeId: string) => Promise<void>;
  resetApp: () => void;
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        selectedSaasType: null,
        isLoading: false,

        // Actions
        setSelectedSaasType: (saasType) => 
          set({ selectedSaasType: saasType }, false, 'setSelectedSaasType'),

        setIsLoading: (loading) => 
          set({ isLoading: loading }, false, 'setIsLoading'),

        handleSaasTypeSelect: async (typeId: string) => {
          set({ isLoading: true }, false, 'handleSaasTypeSelect/start');
          
          // Simulate loading time for better UX
          await new Promise(resolve => setTimeout(resolve, 500));
          
          set({ 
            selectedSaasType: typeId, 
            isLoading: false 
          }, false, 'handleSaasTypeSelect/complete');
        },

        resetApp: () => 
          set({ 
            selectedSaasType: null, 
            isLoading: false 
          }, false, 'resetApp'),
      }),
      {
        name: 'saas-app-store',
        partialize: (state) => ({ 
          selectedSaasType: state.selectedSaasType 
        }),
      }
    ),
    {
      name: 'SaaS App Store',
    }
  )
);
