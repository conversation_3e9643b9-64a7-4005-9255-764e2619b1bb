import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Types for UI state
export interface UIState {
  // Sidebar state
  sidebarOpen: boolean;
  sidebarMounted: boolean;
  
  // Search and filter states
  searchTerm: string;
  sortBy: string;
  selectedCategory: string;
  
  // SaaS Type Selector specific
  saasTypeSortBy: string;
  saasTypeSearchTerm: string;
  
  // Knowledge page specific
  knowledgeSearchTerm: string;
  knowledgeSelectedCategory: string;
  expandedTechs: Set<string>;
  
  // Actions
  setSidebarOpen: (open: boolean) => void;
  setSidebarMounted: (mounted: boolean) => void;
  toggleSidebar: () => void;
  
  setSearchTerm: (term: string) => void;
  setSortBy: (sortBy: string) => void;
  setSelectedCategory: (category: string) => void;
  
  setSaasTypeSortBy: (sortBy: string) => void;
  setSaasTypeSearchTerm: (term: string) => void;
  
  setKnowledgeSearchTerm: (term: string) => void;
  setKnowledgeSelectedCategory: (category: string) => void;
  setExpandedTechs: (techs: Set<string>) => void;
  toggleExpandedTech: (tech: string) => void;
  
  resetUIState: () => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      // Initial state
      sidebarOpen: false,
      sidebarMounted: false,
      
      searchTerm: '',
      sortBy: 'name',
      selectedCategory: 'all',
      
      saasTypeSortBy: 'name',
      saasTypeSearchTerm: '',
      
      knowledgeSearchTerm: '',
      knowledgeSelectedCategory: 'all',
      expandedTechs: new Set<string>(),

      // Sidebar actions
      setSidebarOpen: (open) => 
        set({ sidebarOpen: open }, false, 'setSidebarOpen'),

      setSidebarMounted: (mounted) => 
        set({ sidebarMounted: mounted }, false, 'setSidebarMounted'),

      toggleSidebar: () => 
        set((state) => ({ sidebarOpen: !state.sidebarOpen }), false, 'toggleSidebar'),

      // General search and filter actions
      setSearchTerm: (term) => 
        set({ searchTerm: term }, false, 'setSearchTerm'),

      setSortBy: (sortBy) => 
        set({ sortBy: sortBy }, false, 'setSortBy'),

      setSelectedCategory: (category) => 
        set({ selectedCategory: category }, false, 'setSelectedCategory'),

      // SaaS Type Selector actions
      setSaasTypeSortBy: (sortBy) => 
        set({ saasTypeSortBy: sortBy }, false, 'setSaasTypeSortBy'),

      setSaasTypeSearchTerm: (term) => 
        set({ saasTypeSearchTerm: term }, false, 'setSaasTypeSearchTerm'),

      // Knowledge page actions
      setKnowledgeSearchTerm: (term) => 
        set({ knowledgeSearchTerm: term }, false, 'setKnowledgeSearchTerm'),

      setKnowledgeSelectedCategory: (category) => 
        set({ knowledgeSelectedCategory: category }, false, 'setKnowledgeSelectedCategory'),

      setExpandedTechs: (techs) => 
        set({ expandedTechs: techs }, false, 'setExpandedTechs'),

      toggleExpandedTech: (tech) => 
        set((state) => {
          const newExpanded = new Set(state.expandedTechs);
          if (newExpanded.has(tech)) {
            newExpanded.delete(tech);
          } else {
            newExpanded.add(tech);
          }
          return { expandedTechs: newExpanded };
        }, false, 'toggleExpandedTech'),

      resetUIState: () => 
        set({
          searchTerm: '',
          sortBy: 'name',
          selectedCategory: 'all',
          saasTypeSortBy: 'name',
          saasTypeSearchTerm: '',
          knowledgeSearchTerm: '',
          knowledgeSelectedCategory: 'all',
          expandedTechs: new Set<string>(),
        }, false, 'resetUIState'),
    }),
    {
      name: 'UI Store',
    }
  )
);
