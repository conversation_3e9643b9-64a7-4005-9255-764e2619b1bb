'use client'

import React, { useMemo } from 'react';
import { technologyDetails, techStackData } from '@/lib/techStackData';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Search, ChevronDown, ChevronRight, Filter } from 'lucide-react';
import { useUIStore } from '@/lib/stores';

interface TechnologyWithCategory {
  name: string;
  details: {
    description: string;
    pros: string[];
    cons: string[];
  };
  categories: string[];
}

export default function KnowledgePage() {
  const {
    knowledgeSearchTerm: searchTerm,
    knowledgeSelectedCategory: selectedCategory,
    expandedTechs,
    setKnowledgeSearchTerm: setSearchTerm,
    setKnowledgeSelectedCategory: setSelectedCategory,
    toggleExpandedTech
  } = useUIStore();

  // Process ALL technologies from technologyDetails with their categories
  const technologiesWithCategories = useMemo(() => {
    const techMap = new Map<string, TechnologyWithCategory>();

    // First, add ALL technologies from technologyDetails
    Object.entries(technologyDetails).forEach(([techName, details]) => {
      techMap.set(techName, {
        name: techName,
        details,
        categories: []
      });
    });

    // Then, go through each SaaS type and assign categories to technologies
    Object.values(techStackData.saasTypes).forEach(saasType => {
      Object.entries(saasType.techStack).forEach(([categoryKey, categoryData]) => {
        // Map category keys to proper names, with fallbacks for missing categories
        let categoryName: string;
        const categoryInfo = techStackData.techCategories[categoryKey as keyof typeof techStackData.techCategories];
        if (categoryInfo) {
          categoryName = categoryInfo.name;
        } else {
          // Handle special cases for nested categories
          switch (categoryKey) {
            case 'styling':
              categoryName = 'Frontend';
              break;
            case 'stateManagement':
              categoryName = 'Frontend';
              break;
            default:
              categoryName = categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1);
          }
        }

        // Helper function to process technology arrays
        const processTechArray = (techs: string[]) => {
          techs.forEach(tech => {
            if (techMap.has(tech)) {
              const existing = techMap.get(tech)!;
              if (!existing.categories.includes(categoryName)) {
                existing.categories.push(categoryName);
              }
            }
          });
        };

        // Process primary technologies
        if (categoryData.primary) {
          processTechArray(categoryData.primary);
        }

        // Process alternative technologies
        if (categoryData.alternatives) {
          processTechArray(categoryData.alternatives);
        }

        // Process all other nested arrays (styling, stateManagement, etc.)
        Object.entries(categoryData).forEach(([key, value]) => {
          if (key !== 'primary' && key !== 'alternatives' && Array.isArray(value)) {
            processTechArray(value);
          }
        });
      });
    });

    // For technologies without categories, assign "General" category
    techMap.forEach((tech) => {
      if (tech.categories.length === 0) {
        tech.categories.push('General');
      }
    });

    return Array.from(techMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, []);

  // Filter technologies based on search and category
  const filteredTechnologies = useMemo(() => {
    return technologiesWithCategories.filter(tech => {
      const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tech.details.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || 
                             tech.categories.some(cat => cat.toLowerCase() === selectedCategory.toLowerCase());
      
      return matchesSearch && matchesCategory;
    });
  }, [technologiesWithCategories, searchTerm, selectedCategory]);

  const toggleExpanded = (techName: string) => {
    toggleExpandedTech(techName);
  };

  const categories = useMemo(() => {
    const allCategories = new Set<string>();
    technologiesWithCategories.forEach(tech => {
      tech.categories.forEach(cat => allCategories.add(cat));
    });
    // Ensure "General" appears at the end if it exists
    const sortedCategories = Array.from(allCategories).sort();
    const generalIndex = sortedCategories.indexOf('General');
    if (generalIndex > -1) {
      sortedCategories.splice(generalIndex, 1);
      sortedCategories.push('General');
    }
    return sortedCategories;
  }, [technologiesWithCategories]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Technology Knowledge Base</h1>
          <p className="text-lg text-gray-600">
            Explore all {technologiesWithCategories.length} technologies that our SaaS planner knows about
          </p>
        </div>

        {/* Filters */}
        <Card className="mb-6 bg-white/40 backdrop-blur-sm border-white/30 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search technologies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-white/60 backdrop-blur-sm border-white/40"
                  />
                </div>
              </div>
              <div className="md:w-64">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="bg-white/60 backdrop-blur-sm border-white/40">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category.toLowerCase()}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-sm text-gray-600">
            Showing {filteredTechnologies.length} of {technologiesWithCategories.length} technologies
            {selectedCategory !== 'all' && ` in ${categories.find(cat => cat.toLowerCase() === selectedCategory)}`}
            {searchTerm && ` matching "${searchTerm}"`}
          </p>
        </div>

        {/* Technologies Grid - Wider cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 auto-rows-max">
          {filteredTechnologies.map((tech) => {
            const isExpanded = expandedTechs.has(tech.name);

            return (
              <Card
                key={tech.name}
                className="bg-white/40 backdrop-blur-sm border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] w-full"
              >
                <div className="p-6 flex gap-4">
                  {/* Left side - Title and Categories */}
                  <div className="flex-shrink-0 w-48">
                    <div className="flex items-start justify-between mb-3">
                      <CardTitle className="text-lg leading-tight">
                        {tech.name}
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(tech.name)}
                        className="h-8 w-8 p-0 flex-shrink-0 ml-2"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {tech.categories.map(category => {
                        const categoryInfo = Object.values(techStackData.techCategories).find(
                          cat => cat.name === category
                        );
                        return (
                          <Badge
                            key={category}
                            variant="secondary"
                            className="text-xs"
                            style={categoryInfo ? { backgroundColor: `${categoryInfo.color}20`, color: categoryInfo.color } : {}}
                          >
                            {category}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>

                  {/* Right side - Description */}
                  <div className="flex-1">
                    <CardDescription className="text-sm leading-relaxed">
                      {tech.details.description}
                    </CardDescription>
                  </div>
                </div>

                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <div className="px-6 pb-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Pros */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-green-700 mb-3 flex items-center gap-2 text-sm">
                            <span className="text-green-500">✅</span>
                            Pros ({tech.details.pros.length})
                          </h4>
                          <ul className="space-y-2">
                            {tech.details.pros.map((pro, index) => (
                              <li key={index} className="text-sm text-green-600 flex items-start gap-2">
                                <span className="text-green-500 mt-0.5 flex-shrink-0">•</span>
                                <span className="leading-relaxed">{pro}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Cons */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-red-700 mb-3 flex items-center gap-2 text-sm">
                            <span className="text-red-500">❌</span>
                            Cons ({tech.details.cons.length})
                          </h4>
                          <ul className="space-y-2">
                            {tech.details.cons.map((con, index) => (
                              <li key={index} className="text-sm text-red-600 flex items-start gap-2">
                                <span className="text-red-500 mt-0.5 flex-shrink-0">•</span>
                                <span className="leading-relaxed">{con}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* No results message */}
        {filteredTechnologies.length === 0 && (
          <Card className="bg-white/40 backdrop-blur-sm border-white/30 shadow-xl">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 text-lg">No technologies found matching your criteria.</p>
              <p className="text-gray-400 text-sm mt-2">Try adjusting your search or filter settings.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
