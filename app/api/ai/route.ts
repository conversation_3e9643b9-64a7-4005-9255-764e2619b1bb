import { NextResponse, NextRequest} from 'next/server'

export async function GET(request: Request) {
    const url = new URL(request.url)
    
    fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${process.env.AI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'openai/gpt-4o',
          messages: [
            {
              role: 'user',
              content: 'What is the meaning of life?',
            },
          ],
        }),
      });
      
}