'use client'
import React, { useMemo } from 'react';
import SaasTypeSelector from '@/components/SaasTypeSelector';
import TechStackVisualizer from '@/components/TechStackVisualizer';
import TechDetailsPanel from '@/components/TechDetailsPanel';
import ProjectSidebar from '@/components/ProjectSidebar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Skeleton } from '@/components/ui/skeleton';
import { techStackData } from '@/lib/techStackData';
import { useAppStore } from '@/lib/stores';

export default function TechStackPlanner() {
  const {
    selectedSaasType,
    isLoading,
    handleSaasTypeSelect,
    setSelectedSaasType,
    isCustomProject,
    currentProject,
    setCurrentProject
  } = useAppStore();

  const saasTypes = useMemo(() =>
    Object.entries(techStackData.saasTypes).map(([id, data]) => ({
      id,
      name: data.name,
      description: data.description,
    })), []
  );

  const selectedSaasData = useMemo(() =>
    selectedSaasType ? techStackData.saasTypes[selectedSaasType as keyof typeof techStackData.saasTypes] : null,
    [selectedSaasType]
  );

  const currentTechStack = useMemo(() => {
    if (isCustomProject && currentProject) {
      return currentProject.techStack;
    }
    return selectedSaasData?.techStack || {};
  }, [isCustomProject, currentProject, selectedSaasData]);

  const currentProjectName = useMemo(() => {
    if (isCustomProject && currentProject) {
      return currentProject.name;
    }
    return selectedSaasData?.name || '';
  }, [isCustomProject, currentProject, selectedSaasData]);

  const currentProjectDescription = useMemo(() => {
    if (isCustomProject && currentProject) {
      return currentProject.description;
    }
    return selectedSaasData?.description || '';
  }, [isCustomProject, currentProject, selectedSaasData]);

  const handleBackToSelector = () => {
    if (isCustomProject) {
      setCurrentProject(null);
    } else {
      setSelectedSaasType(null);
    }
  };

  return (
    <div className="min-h-screen min-w-screen bg-gray-50 dark:bg-gray-900">
      {!selectedSaasType && !isCustomProject ? (
        <SaasTypeSelector
          saasTypes={saasTypes}
          selectedType={selectedSaasType}
          onTypeSelect={handleSaasTypeSelect}
        />
      ) : isLoading ? (
        <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          <div className="w-80 p-4">
            <Skeleton className="w-full h-full rounded-xl" />
          </div>
          <Separator orientation="vertical" className="h-full" />
          <div className="flex-1 p-6">
            <div className="h-full flex flex-col">
              <Skeleton className="h-8 w-64 mb-4" />
              <Skeleton className="h-6 w-96 mb-6" />
              <Skeleton className="flex-1 w-full rounded-xl" />
            </div>
          </div>
        </div>
      ) : (
        <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
          {/* Left Panel - Tech Details or Project Sidebar */}
          <div className="w-80 p-4">
            {isCustomProject ? (
              <ProjectSidebar isVisible={true} />
            ) : selectedSaasData ? (
              <TechDetailsPanel
                techStack={selectedSaasData.techStack}
                techCategories={techStackData.techCategories}
                saasTypeName={selectedSaasData.name}
                onTechSelect={(tech, category) => {
                  console.log('Selected tech:', tech, 'in category:', category);
                }}
              />
            ) : null}
          </div>

          <Separator orientation="vertical" className="h-full" />

          {/* Right Panel - Visualization */}
          <div className="flex-1 p-6">
            <div className="h-full flex flex-col">
              {/* Breadcrumb Navigation */}
              <Breadcrumb className="mb-4">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="#" onClick={handleBackToSelector}>
                      {isCustomProject ? 'Projects' : 'SaaS Types'}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{currentProjectName}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <div className="flex items-center justify-between mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {currentProjectName} {isCustomProject ? 'Project' : 'Tech Stack'}
                  </h1>
                  <p className="text-gray-600">
                    {currentProjectDescription}
                  </p>
                  {isCustomProject && (
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        Custom Project
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {Object.keys(currentTechStack).length} categories
                      </span>
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleBackToSelector}
                  variant="outline"
                  size="default"
                >
                  {isCustomProject ? 'Back to Projects' : 'Change SaaS Type'}
                </Button>
              </div>

              <div className="flex-1">
                {(selectedSaasData || isCustomProject) && (
                  <TechStackVisualizer
                    techStack={currentTechStack}
                    techCategories={techStackData.techCategories}
                    saasTypeName={currentProjectName}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}