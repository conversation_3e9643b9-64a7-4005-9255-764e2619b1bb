'use client'

import React, { useEffect, useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { GripVertical, ChevronDown, ChevronRight } from 'lucide-react';
import { technologyDetails } from '@/lib/techStackData';
import { useTechStackStore } from '@/lib/stores';

interface TechItem {
  id: string;
  name: string;
  type: 'primary' | 'alternative';
}

interface TechCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryName: string;
  categoryColor: string;
  categoryDescription: string;
  primaryTechnologies: string[];
  alternativeTechnologies: string[];
  onTechnologiesUpdate: (primary: string[], alternatives: string[]) => void;
}

function SortableItem({ tech, categoryColor }: { tech: TechItem; categoryColor: string }) {
  const [prosExpanded, setProsExpanded] = useState(false);
  const [consExpanded, setConsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: tech.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const techDetails = technologyDetails[tech.name as keyof typeof technologyDetails];

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`transition-all duration-200 ${
        isDragging ? 'opacity-50 scale-105 shadow-lg' : 'hover:shadow-md'
      }`}
    >
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-sm">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
          >
            <GripVertical className="h-4 w-4 text-gray-400" />
          </div>
          {tech.name}
          <Badge 
            variant={tech.type === 'primary' ? 'default' : 'outline'} 
            className="text-xs ml-auto"
            style={tech.type === 'primary' ? { backgroundColor: categoryColor } : {}}
          >
            {tech.type === 'primary' ? 'Primary' : 'Alternative'}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      {techDetails && (
        <CardContent className="pt-0">
          <CardDescription className="text-xs mb-2">
            {techDetails.description}
          </CardDescription>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            {/* Pros Section */}
            <div>
              <Collapsible open={prosExpanded} onOpenChange={setProsExpanded}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 font-medium text-green-700 hover:text-green-800 flex items-center gap-1"
                  >
                    {prosExpanded ? (
                      <ChevronDown className="h-3 w-3" />
                    ) : (
                      <ChevronRight className="h-3 w-3" />
                    )}
                    Pros ({techDetails.pros.length})
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-1">
                  <ul className="space-y-0.5">
                    {techDetails.pros.map((pro, index) => (
                      <li key={index} className="text-green-600">• {pro}</li>
                    ))}
                  </ul>
                </CollapsibleContent>
              </Collapsible>

              {/* Show preview when collapsed */}
              {!prosExpanded && (
                <ul className="space-y-0.5 mt-1">
                  {techDetails.pros.slice(0, 2).map((pro, index) => (
                    <li key={index} className="text-green-600">• {pro}</li>
                  ))}
                  {techDetails.pros.length > 2 && (
                    <li className="text-gray-500">+{techDetails.pros.length - 2} more</li>
                  )}
                </ul>
              )}
            </div>

            {/* Cons Section */}
            <div>
              <Collapsible open={consExpanded} onOpenChange={setConsExpanded}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 font-medium text-red-700 hover:text-red-800 flex items-center gap-1"
                  >
                    {consExpanded ? (
                      <ChevronDown className="h-3 w-3" />
                    ) : (
                      <ChevronRight className="h-3 w-3" />
                    )}
                    Cons ({techDetails.cons.length})
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-1">
                  <ul className="space-y-0.5">
                    {techDetails.cons.map((con, index) => (
                      <li key={index} className="text-red-600">• {con}</li>
                    ))}
                  </ul>
                </CollapsibleContent>
              </Collapsible>

              {/* Show preview when collapsed */}
              {!consExpanded && (
                <ul className="space-y-0.5 mt-1">
                  {techDetails.cons.slice(0, 2).map((con, index) => (
                    <li key={index} className="text-red-600">• {con}</li>
                  ))}
                  {techDetails.cons.length > 2 && (
                    <li className="text-gray-500">+{techDetails.cons.length - 2} more</li>
                  )}
                </ul>
              )}
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

export default function TechCategoryModal({
  isOpen,
  onClose,
  categoryName,
  categoryColor,
  categoryDescription,
  primaryTechnologies,
  alternativeTechnologies,
  onTechnologiesUpdate,
}: TechCategoryModalProps) {
  const {
    modalTechnologies: technologies,
    setModalTechnologies: setTechnologies,
    initializeModalTechnologies,
    reorderModalTechnologies
  } = useTechStackStore();

  // Initialize technologies when modal opens or props change
  useEffect(() => {
    if (isOpen) {
      initializeModalTechnologies(primaryTechnologies, alternativeTechnologies);
    }
  }, [isOpen, primaryTechnologies, alternativeTechnologies, initializeModalTechnologies]);



  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = technologies.findIndex((item) => item.id === active.id);
      const newIndex = technologies.findIndex((item) => item.id === over?.id);

      const newItems = arrayMove(technologies, oldIndex, newIndex);

      // Update store
      reorderModalTechnologies(newItems);

      // Update parent component
      const newPrimary = newItems.filter(item => item.type === 'primary').map(item => item.name);
      const newAlternatives = newItems.filter(item => item.type === 'alternative').map(item => item.name);
      onTechnologiesUpdate(newPrimary, newAlternatives);
    }
  }



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: categoryColor }}
            />
            Reorder {categoryName} Technologies
          </DialogTitle>
          <DialogDescription>
            {categoryDescription} • Drag and drop to reorder technologies by priority
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <ScrollArea className="h-[500px]">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={technologies} strategy={verticalListSortingStrategy}>
                <div className="space-y-3">
                  {technologies.map((tech) => (
                    <SortableItem
                      key={tech.id}
                      tech={tech}
                      categoryColor={categoryColor}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}
