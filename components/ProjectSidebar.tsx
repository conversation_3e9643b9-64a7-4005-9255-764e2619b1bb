'use client'

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, Edit3 } from 'lucide-react';
import { useAppStore } from '@/lib/stores';
import { techStackData } from '@/lib/techStackData';
import AddTechnologyModal from './AddTechnologyModal';

interface ProjectSidebarProps {
  isVisible: boolean;
}

export default function ProjectSidebar({ isVisible }: ProjectSidebarProps) {
  const { 
    currentProject, 
    isCustomProject, 
    removeTechnologyFromProject,
    updateCurrentProject 
  } = useAppStore();
  
  const [isAddTechModalOpen, setIsAddTechModalOpen] = useState(false);

  if (!isVisible || !isCustomProject || !currentProject) {
    return null;
  }

  const handleRemoveTechnology = (category: string, technology: string) => {
    removeTechnologyFromProject(category, technology);
  };

  const getCategoryInfo = (categoryKey: string) => {
    return techStackData.techCategories[categoryKey as keyof typeof techStackData.techCategories];
  };

  const totalTechnologies = Object.values(currentProject.techStack).reduce(
    (total, category) => total + category.primary.length + category.alternatives.length,
    0
  );

  return (
    <>
      <div className="w-80 bg-white/80 backdrop-blur-md border-r border-white/30 shadow-xl h-full overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-white/30">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold text-gray-900">Project Technologies</h2>
            <Badge variant="secondary" className="text-xs">
              {totalTechnologies} total
            </Badge>
          </div>
          <p className="text-sm text-gray-600 mb-3">{currentProject.name}</p>
          
          <Button 
            onClick={() => setIsAddTechModalOpen(true)}
            className="w-full"
            size="sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Technology
          </Button>
        </div>

        {/* Technology List */}
        <ScrollArea className="flex-1 p-4">
          {Object.keys(currentProject.techStack).length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="mb-4">
                <Plus className="h-12 w-12 mx-auto text-gray-300" />
              </div>
              <p className="text-sm">No technologies added yet.</p>
              <p className="text-xs text-gray-400 mt-1">
                Click "Add Technology" to get started.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(currentProject.techStack).map(([categoryKey, categoryData]) => {
                const categoryInfo = getCategoryInfo(categoryKey);
                const allTechs = [...categoryData.primary, ...categoryData.alternatives];
                
                if (allTechs.length === 0) return null;

                return (
                  <Card key={categoryKey} className="bg-white/60 backdrop-blur-sm border-white/40">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: categoryInfo?.color || '#6B7280' }}
                        />
                        {categoryInfo?.name || categoryKey}
                        <Badge variant="outline" className="text-xs ml-auto">
                          {allTechs.length}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {/* Primary Technologies */}
                        {categoryData.primary.map((tech) => (
                          <div
                            key={`${categoryKey}-primary-${tech}`}
                            className="flex items-center justify-between p-2 bg-white/40 rounded-lg border border-white/30"
                          >
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant="default" 
                                className="text-xs"
                                style={{ backgroundColor: categoryInfo?.color || '#6B7280' }}
                              >
                                Primary
                              </Badge>
                              <span className="text-sm font-medium">{tech}</span>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                              onClick={() => handleRemoveTechnology(categoryKey, tech)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}

                        {/* Alternative Technologies */}
                        {categoryData.alternatives.map((tech) => (
                          <div
                            key={`${categoryKey}-alt-${tech}`}
                            className="flex items-center justify-between p-2 bg-gray-50/40 rounded-lg border border-gray-200/50"
                          >
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                Alt
                              </Badge>
                              <span className="text-sm">{tech}</span>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                              onClick={() => handleRemoveTechnology(categoryKey, tech)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </ScrollArea>

        {/* Footer */}
        <div className="p-4 border-t border-white/30">
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex justify-between">
              <span>Categories:</span>
              <span>{Object.keys(currentProject.techStack).length}</span>
            </div>
            <div className="flex justify-between">
              <span>Primary:</span>
              <span>
                {Object.values(currentProject.techStack).reduce(
                  (total, cat) => total + cat.primary.length, 0
                )}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Alternatives:</span>
              <span>
                {Object.values(currentProject.techStack).reduce(
                  (total, cat) => total + cat.alternatives.length, 0
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Add Technology Modal */}
      <AddTechnologyModal
        isOpen={isAddTechModalOpen}
        onClose={() => setIsAddTechModalOpen(false)}
      />
    </>
  );
}
