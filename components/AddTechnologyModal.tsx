'use client'

import React, { useState, useMemo } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Check } from 'lucide-react';
import { technologyDetails, techStackData } from '@/lib/techStackData';
import { useAppStore } from '@/lib/stores';

interface AddTechnologyModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TechnologyWithCategory {
  name: string;
  details: {
    description: string;
    pros: string[];
    cons: string[];
  };
  categories: string[];
}

export default function AddTechnologyModal({ isOpen, onClose }: AddTechnologyModalProps) {
  const { currentProject, addTechnologyToProject } = useAppStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTechCategory, setSelectedTechCategory] = useState<string>('');

  // Process ALL technologies from technologyDetails with their categories
  const technologiesWithCategories = useMemo(() => {
    const techMap = new Map<string, TechnologyWithCategory>();

    // First, add ALL technologies from technologyDetails
    Object.entries(technologyDetails).forEach(([techName, details]) => {
      techMap.set(techName, {
        name: techName,
        details,
        categories: []
      });
    });

    // Then, go through each SaaS type and assign categories to technologies
    Object.values(techStackData.saasTypes).forEach(saasType => {
      Object.entries(saasType.techStack).forEach(([categoryKey, categoryData]) => {
        const categoryInfo = techStackData.techCategories[categoryKey as keyof typeof techStackData.techCategories];
        const categoryName = categoryInfo ? categoryInfo.name : categoryKey;

        // Add primary technologies
        if (categoryData.primary) {
          categoryData.primary.forEach(techName => {
            const tech = techMap.get(techName);
            if (tech && !tech.categories.includes(categoryName)) {
              tech.categories.push(categoryName);
            }
          });
        }

        // Add alternative technologies
        if (categoryData.alternatives) {
          categoryData.alternatives.forEach(techName => {
            const tech = techMap.get(techName);
            if (tech && !tech.categories.includes(categoryName)) {
              tech.categories.push(categoryName);
            }
          });
        }
      });
    });

    return Array.from(techMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, []);

  // Get unique categories
  const categories = useMemo(() => {
    const allCategories = new Set<string>();
    technologiesWithCategories.forEach(tech => {
      tech.categories.forEach(cat => allCategories.add(cat));
    });
    return ['all', ...Array.from(allCategories).sort()];
  }, [technologiesWithCategories]);

  // Filter technologies based on search and category
  const filteredTechnologies = useMemo(() => {
    return technologiesWithCategories.filter(tech => {
      const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tech.details.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || tech.categories.includes(selectedCategory);
      return matchesSearch && matchesCategory;
    });
  }, [technologiesWithCategories, searchTerm, selectedCategory]);

  // Check if technology is already in project
  const isTechnologyInProject = (techName: string) => {
    if (!currentProject) return false;
    
    return Object.values(currentProject.techStack).some(category => 
      category.primary.includes(techName) || category.alternatives.includes(techName)
    );
  };

  const handleAddTechnology = (techName: string, isPrimary: boolean) => {
    if (!selectedTechCategory) {
      alert('Please select a category first');
      return;
    }
    
    addTechnologyToProject(selectedTechCategory, techName, isPrimary);
  };

  const techCategories = Object.entries(techStackData.techCategories).map(([key, value]) => ({
    key,
    name: value.name,
    description: value.description
  }));

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Add Technology to Project</DialogTitle>
          <DialogDescription>
            Select technologies from our knowledge base to add to your project.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Category Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Add to Category</label>
              <Select value={selectedTechCategory} onValueChange={setSelectedTechCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {techCategories.map((category) => (
                    <SelectItem key={category.key} value={category.key}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Filter by Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search technologies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Technology List */}
          <ScrollArea className="h-96">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
              {filteredTechnologies.map((tech) => {
                const isInProject = isTechnologyInProject(tech.name);
                
                return (
                  <Card key={tech.name} className={`transition-all ${isInProject ? 'opacity-50' : 'hover:shadow-md'}`}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        {tech.name}
                        {isInProject && (
                          <Badge variant="secondary" className="text-xs">
                            <Check className="h-3 w-3 mr-1" />
                            Added
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription className="text-xs line-clamp-2">
                        {tech.details.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex flex-wrap gap-1 mb-3">
                        {tech.categories.slice(0, 3).map((category) => (
                          <Badge key={category} variant="outline" className="text-xs">
                            {category}
                          </Badge>
                        ))}
                        {tech.categories.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{tech.categories.length - 3}
                          </Badge>
                        )}
                      </div>
                      
                      {!isInProject && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="default"
                            className="flex-1 text-xs"
                            onClick={() => handleAddTechnology(tech.name, true)}
                            disabled={!selectedTechCategory}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Primary
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex-1 text-xs"
                            onClick={() => handleAddTechnology(tech.name, false)}
                            disabled={!selectedTechCategory}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Alternative
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>

          {filteredTechnologies.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No technologies found matching your criteria.
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
