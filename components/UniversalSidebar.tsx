
'use client'

import React from 'react'
import { Settings, House, Folder, BookOpen, ChevronLeft, ChevronRight} from 'lucide-react'

const SidebarData = [
    {
        name: "Home",
        href: "/",
        icon: <House/>,
    },
    {
        name: "Projects",
        href: "/projects",
        icon: <Folder/>,
    },
    {
        name: "Knowledge",
        href: "/knowledge",
        icon: <BookOpen/>,
    },
]



export default function AppSidebar() {
    const [open, setOpen] = React.useState(false)
    const [mounted, setMounted] = React.useState(false)

    React.useEffect(() => {
        setMounted(true)
    }, [])

    const toggleSidebar = () => setOpen(!open)

    if (!mounted) return null

    return (
        <>
            {/* Backdrop overlay when sidebar is open */}
            {open && (
                <div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[998]"
                    onClick={() => setOpen(false)}
                />
            )}

            <div className={`
                fixed left-0 top-0 h-full z-[999] w-64
                transform transition-transform duration-300 ease-in-out
                ${open ? 'translate-x-0' : '-translate-x-full'}
                shadow-2xl bg-white border-r flex flex-col
            `}>
                {/* Header */}
                <div className="flex flex-col gap-2 p-2">
                </div>

                {/* Content */}
                <div className="flex min-h-0 flex-1 flex-col gap-2 overflow-auto p-2">
                  {SidebarData.map((item) => (
                      <a
                        key={item.name}
                        href={item.href}
                        className="flex w-full items-center gap-2 rounded-md p-2 text-left hover:bg-gray-100 transition-colors"
                      >
                        {item.icon}
                        <span>{item.name}</span>
                      </a>
                   ))}
                </div>

                {/* Footer */}
                <div className="flex flex-col gap-2 p-2 border-t">
                    <a
                      href="/settings"
                      className="flex w-full items-center gap-2 rounded-md p-2 text-left hover:bg-gray-100 transition-colors"
                    >
                      <Settings />
                      <span>Settings</span>
                    </a>

                    <div className="flex items-center gap-2 p-2">
                      <div className="h-8 w-8 rounded-lg bg-gray-200 flex items-center justify-center">
                        <span className="text-sm font-medium">CN</span>
                      </div>
                      <div className="flex-1 text-sm">
                        <div className="font-medium">shadcn</div>
                        <div className="text-gray-500 text-xs"><EMAIL></div>
                      </div>
                    </div>
                </div>
            </div>

            {/* Custom Trigger */}
            <button
                onClick={toggleSidebar}
                className={`
                    fixed top-1/2 -translate-y-1/2 z-[1000] h-16 w-8 rounded-r-lg rounded-l-none
                    bg-white/80 backdrop-blur-sm border border-l-0 border-gray-300
                    shadow-lg hover:shadow-xl transition-all duration-300
                    flex items-center justify-center group
                    ${open ? 'left-64' : 'left-0'}
                `}
            >
                {open ? (
                    <ChevronLeft className="h-4 w-4 text-gray-600 group-hover:text-gray-800 transition-colors" />
                ) : (
                    <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-gray-800 transition-colors" />
                )}
            </button>
        </>
    )
}