'use client'

import React, { useMemo } from 'react';
import { Plus } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useUIStore, useAppStore } from '@/lib/stores';

interface SaasType {
  id: string;
  name: string;
  description: string;
}

interface SaasTypeSelectorProps {
  saasTypes: SaasType[];
  selectedType: string | null;
  onTypeSelect: (typeId: string) => void;
}

export default function SaasTypeSelector({ saasTypes, selectedType, onTypeSelect }: SaasTypeSelectorProps) {
  const {
    saasTypeSortBy: sortBy,
    saasTypeSearchTerm: searchTerm,
    setSaasTypeSortBy: setSortBy,
    setSaasTypeSearchTerm: setSearchTerm
  } = useUIStore();

  const { createNewProject } = useAppStore();

  const [isNewProjectDialogOpen, setIsNewProjectDialogOpen] = React.useState(false);
  const [newProjectName, setNewProjectName] = React.useState('');
  const [newProjectDescription, setNewProjectDescription] = React.useState('');

  const handleCreateProject = () => {
    if (newProjectName.trim()) {
      createNewProject(newProjectName.trim(), newProjectDescription.trim());
      setIsNewProjectDialogOpen(false);
      setNewProjectName('');
      setNewProjectDescription('');
    }
  };

  const sortedSaasTypes = useMemo(() => {
    let filtered = [...saasTypes];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(type =>
        type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        type.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort
    return filtered.sort((a, b) => {
      if (sortBy === "name") {
        return a.name.localeCompare(b.name);
      }
      return 0;
    });
  }, [saasTypes, searchTerm, sortBy]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          SaaS Tech Stack Planner
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Choose your SaaS type to visualize the recommended tech stack
        </p>

        {/* Filter Controls */}
        <div className="mt-6 flex justify-center">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="complexity">Complexity</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='flex items-center px-2'>
          <Input
            placeholder="Search technologies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-white/60 backdrop-blur-sm border-white/40"
          />
          </div>
          <div>
            <Dialog open={isNewProjectDialogOpen} onOpenChange={setIsNewProjectDialogOpen}>
              <DialogTrigger asChild>
                <Button><Plus className="mr-2 h-4 w-4" />New Project</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Create New Project</DialogTitle>
                  <DialogDescription>
                    Create a custom tech stack project. You can add technologies from our knowledge base.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="project-name">Project Name</Label>
                    <Input
                      id="project-name"
                      placeholder="My Awesome SaaS"
                      value={newProjectName}
                      onChange={(e) => setNewProjectName(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="project-description">Description</Label>
                    <Textarea
                      id="project-description"
                      placeholder="Describe your project..."
                      value={newProjectDescription}
                      onChange={(e) => setNewProjectDescription(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsNewProjectDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateProject} disabled={!newProjectName.trim()}>
                    Create Project
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
      
      <div className="">
        <div className='flex justify-left items-center mb-4'>
          <div>
            <h2>Recent Projects</h2>
          </div>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {sortedSaasTypes.map((type) => (
          <Card
            key={type.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedType === type.id
                ? 'ring-2 ring-primary bg-primary/5'
                : 'hover:bg-accent/50'
            }`}
            onClick={() => onTypeSelect(type.id)}
          >
            <CardHeader>
              <CardTitle className="text-lg font-semibold flex items-center justify-between">
                {type.name}
                <Badge variant="secondary" className="text-xs">
                  {type.id === 'saas_starter' ? 'Starter' :
                   type.id === 'ecommerce' ? 'Popular' :
                   type.id === 'analytics' ? 'Advanced' : 'Standard'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-sm">
                {type.description}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
        </div>
      </div>
      
      {selectedType && (
        <div className="mt-8 flex justify-center">
          <Badge variant="outline" className="text-sm px-4 py-2">
            Selected: {sortedSaasTypes.find(t => t.id === selectedType)?.name}
          </Badge>
        </div>
      )}
    </div>
  );
}
