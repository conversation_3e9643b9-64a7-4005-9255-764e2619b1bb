'use client'

import React from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';

interface TechNodeData {
  label: string;
  category: string;
  color: string;
  primaryTechnologies: string[];
  alternativeTechnologies: string[];
  description?: string;
  onNodeClick?: () => void;
}

interface TechNodeProps {
  data: TechNodeData;
  selected?: boolean;
}

export function TechCategoryNode({ data, selected }: TechNodeProps) {
  const allTechnologies = [...data.primaryTechnologies, ...data.alternativeTechnologies];

  return (
    <Card
      className={`min-w-[280px] max-w-[320px] transition-all duration-300 hover:scale-105 cursor-pointer backdrop-blur-md border-2 ${
        selected
          ? 'bg-white/30 border-white/40 ring-2 ring-white/50 shadow-2xl'
          : 'bg-white/20 border-white/30 hover:bg-white/25 hover:border-white/40 shadow-xl hover:shadow-2xl'
      }`}
      style={{
        borderColor: selected ? data.color : `${data.color}40`,
        boxShadow: selected
          ? `0 25px 50px -12px ${data.color}40, 0 0 0 1px ${data.color}20`
          : `0 20px 25px -5px ${data.color}20, 0 10px 10px -5px ${data.color}10`
      }}
      onClick={data.onNodeClick}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 border-2 border-white/50"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />

      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <div
            className="w-4 h-4 rounded-full shadow-lg animate-pulse"
            style={{
              background: `linear-gradient(135deg, ${data.color}, ${data.color}80)`,
              boxShadow: `0 0 10px ${data.color}40`
            }}
          />
          {data.label}
          <Badge variant="secondary" className="text-xs ml-auto">
            {allTechnologies.length}
          </Badge>
        </CardTitle>
        {data.description && (
          <p className="text-xs text-muted-foreground line-clamp-2">{data.description}</p>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        <ScrollArea className="h-32">
          <div className="space-y-2">
            {/* Primary Technologies */}
            {data.primaryTechnologies.map((tech, index) => (
              <div
                key={`primary-${index}`}
                className="flex items-center justify-between p-2 bg-white/40 backdrop-blur-sm rounded-lg border border-white/30 hover:bg-white/50 transition-all duration-200"
              >
                <span className="text-xs font-medium text-gray-800">{tech}</span>
                <Badge variant="default" className="text-xs" style={{ backgroundColor: data.color }}>
                  Primary
                </Badge>
              </div>
            ))}

            {/* Alternative Technologies */}
            {data.alternativeTechnologies.map((tech, index) => (
              <div
                key={`alt-${index}`}
                className="flex items-center justify-between p-2 bg-gray-50/40 backdrop-blur-sm rounded-lg border border-gray-200/50 hover:bg-gray-100/50 transition-all duration-200"
              >
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    Alt
                  </Badge>
                  <span className="text-xs font-medium text-gray-700">{tech}</span>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <Button
          variant="ghost"
          size="sm"
          className="w-full mt-3 text-xs"
          onClick={(e) => {
            e.stopPropagation();
            data.onNodeClick?.();
          }}
        >
          View Details & Reorder
        </Button>
      </CardContent>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 border-2 border-white/50"
        style={{ background: `linear-gradient(135deg, ${data.color}, ${data.color}80)` }}
      />
    </Card>
  );
}

// Node types for ReactFlow
export const nodeTypes = {
  techCategory: TechCategoryNode,
};
